# 📖 FX Backtester - Benutzeranleitung

## 🚀 Schnellstart

### 1. **App starten**
```bash
cd fx-backtester
npm run dev
```
<PERSON><PERSON><PERSON> dann http://localhost:3000 in deinem Browser.

### 2. **CSV-Daten importieren**
- **Beispiel-Daten verwenden:** Lade die mitgelieferte `sample_data.csv` Datei hoch
- **Eigene Daten:** Exportiere Daten aus MetaTrader im CSV-Format

### 3. **Trading beginnen**
- Klicke auf "Start Trading" nach erfolgreichem Import
- Die Chart-Ansicht öffnet sich mit deinen historischen Daten

## 📊 Interface-Übersicht

### **Hauptbereiche:**
1. **Header:** Account-Übersicht (Balance, Equity, P&L)
2. **Chart:** Candlestick-Chart mit TradingView
3. **Playback Controls:** Zeit-Navigation und Geschwindigkeit
4. **Order Panel:** Buy/Sell Orders platzieren
5. **Performance Panel:** Trading-Metriken
6. **Positions:** Offene Trades
7. **Order History:** Vergangene Trades

## ⏯️ Playback-Kontrollen

### **Grundfunktionen:**
- **▶️ Play:** Automatische Wiedergabe der historischen Daten
- **⏸️ Pause:** Wiedergabe anhalten
- **⏮️ Step Back:** Eine Kerze zurück
- **⏭️ Step Forward:** Eine Kerze vor
- **🔄 Reset:** Zurück zum Anfang

### **Geschwindigkeits-Einstellungen:**
- **Fast (100ms):** Sehr schnelle Wiedergabe
- **Medium (500ms):** Mittlere Geschwindigkeit
- **Normal (1s):** Standard-Geschwindigkeit
- **Slow (2s):** Langsame, detaillierte Analyse

## 💰 Trading-Funktionen

### **Order-Platzierung:**
1. **Order-Typ wählen:** Buy (Long) oder Sell (Short)
2. **Position Size:** Lot-Größe eingeben (0.01 - 100)
3. **Stop Loss (Optional):** Verlust-Begrenzung setzen
4. **Take Profit (Optional):** Gewinn-Mitnahme setzen
5. **Order platzieren:** Klick auf BUY/SELL Button

### **Quick-Size Buttons:**
- **0.01 Lots:** Micro Lot (1,000 Einheiten)
- **0.1 Lots:** Mini Lot (10,000 Einheiten)
- **0.5 Lots:** Halber Standard Lot
- **1.0 Lots:** Standard Lot (100,000 Einheiten)

### **Margin-Berechnung:**
- **Leverage:** 1:100 (Standard)
- **Required Margin:** Position Value / 100
- **Free Margin:** Verfügbares Kapital für neue Trades

## 📈 Chart-Features

### **Visualisierung:**
- **Candlesticks:** OHLC-Darstellung der Kurse
- **Order-Marker:** 🔺 Buy Entry, 🔻 Sell Entry
- **Exit-Marker:** ⭕ Trade-Schließung mit P&L
- **Stop Loss Lines:** 🔴 Gestrichelte rote Linien
- **Take Profit Lines:** 🟢 Gestrichelte grüne Linien
- **Current Price:** 🔵 Aktuelle Kurs-Linie

### **Navigation:**
- **Zoom:** Mausrad oder Pinch-Geste
- **Pan:** Klicken und ziehen
- **Auto-Scroll:** Folgt automatisch der aktuellen Kerze

## 📊 Performance-Analyse

### **Haupt-Metriken:**
- **Total Return:** Gesamtrendite in % und absolut
- **Win Rate:** Prozentsatz gewinnender Trades
- **Profit Factor:** Verhältnis Gewinne zu Verlusten
- **Max Drawdown:** Größter Verlust vom Höchststand

### **Detaillierte Statistiken:**
- **Total Trades:** Anzahl abgeschlossener Trades
- **Winning/Losing Trades:** Aufschlüsselung nach Erfolg
- **Average Win/Loss:** Durchschnittliche Gewinne/Verluste
- **Largest Win/Loss:** Größte einzelne Gewinne/Verluste
- **Sharpe Ratio:** Risiko-adjustierte Rendite

### **Performance-Bewertung:**
- **🎯 Excellent:** Win Rate ≥60%, Profit Factor ≥1.5
- **👍 Good:** Win Rate ≥50%, Profit Factor ≥1.2
- **⚠️ Average:** Win Rate ≥40%, Profit Factor ≥1.0
- **📉 Needs Improvement:** Darunter liegende Werte

## 🎯 Trading-Strategien testen

### **Trend-Following:**
1. Warte auf klare Aufwärts-/Abwärtstrends
2. Platziere Orders in Trend-Richtung
3. Setze Stop Loss gegen den Trend
4. Take Profit bei Trend-Umkehr

### **Range-Trading:**
1. Identifiziere Unterstützung/Widerstand
2. Buy bei Unterstützung, Sell bei Widerstand
3. Enge Stop Loss außerhalb der Range
4. Take Profit am gegenüberliegenden Level

### **Breakout-Trading:**
1. Warte auf Konsolidierung
2. Platziere Orders bei Breakout-Levels
3. Stop Loss in der alten Range
4. Take Profit bei nächstem Widerstand

## ⚠️ Risk Management

### **Position Sizing:**
- **Nie mehr als 2-5% des Kapitals pro Trade riskieren**
- **Kleinere Positionen bei unsicheren Setups**
- **Größere Positionen nur bei hoher Wahrscheinlichkeit**

### **Stop Loss Regeln:**
- **Immer Stop Loss setzen**
- **Basierend auf technischen Levels**
- **Nicht emotional verschieben**
- **Risk/Reward Ratio mindestens 1:2**

### **Diversifikation:**
- **Nicht alle Trades in eine Richtung**
- **Verschiedene Zeitpunkte für Entries**
- **Maximum 3-5 offene Positionen gleichzeitig**

## 🔧 Tipps & Tricks

### **Effiziente Nutzung:**
1. **Keyboard Shortcuts:** Leertaste für Play/Pause
2. **Quick Orders:** Nutze die Quick-Size Buttons
3. **Multiple Tests:** Reset und teste verschiedene Strategien
4. **Performance Tracking:** Notiere dir erfolgreiche Setups

### **Häufige Fehler vermeiden:**
- ❌ **Overtrading:** Zu viele Trades ohne klaren Plan
- ❌ **No Stop Loss:** Trades ohne Verlust-Begrenzung
- ❌ **Emotional Trading:** Entscheidungen aus Angst/Gier
- ❌ **Ignoring Risk:** Zu große Positionen für das Konto

### **Best Practices:**
- ✅ **Trading Plan:** Klare Ein-/Ausstiegs-Regeln
- ✅ **Backtesting:** Verschiedene Marktbedingungen testen
- ✅ **Journaling:** Erfolgreiche Trades dokumentieren
- ✅ **Continuous Learning:** Strategien kontinuierlich verbessern

## 📄 CSV-Format Details

### **MetaTrader Export:**
1. **MT4/MT5 öffnen**
2. **History Center → Export**
3. **Format:** CSV mit allen Spalten
4. **Timeframe:** M1, M5, M15, H1, etc.

### **Erwartetes Format:**
```csv
DATE,TIME,OPEN,HIGH,LOW,CLOSE,TICKVOL,VOL,SPREAD
2025.01.20,09:00:00,1.11783,1.11790,1.11775,1.11785,15,0,2
```

### **Unterstützte Timeframes:**
- **M1:** 1-Minuten-Kerzen (sehr detailliert)
- **M5:** 5-Minuten-Kerzen (Scalping)
- **M15:** 15-Minuten-Kerzen (Intraday)
- **H1:** 1-Stunden-Kerzen (Swing Trading)
- **H4:** 4-Stunden-Kerzen (Position Trading)
- **D1:** Tages-Kerzen (Long-term)

## 🆘 Troubleshooting

### **Häufige Probleme:**

**CSV Import funktioniert nicht:**
- ✅ Prüfe das Datei-Format (muss .csv sein)
- ✅ Stelle sicher, dass alle Spalten vorhanden sind
- ✅ Überprüfe das Datum/Zeit-Format

**Chart wird nicht angezeigt:**
- ✅ Aktualisiere die Seite (F5)
- ✅ Prüfe die Browser-Konsole auf Fehler
- ✅ Verwende einen modernen Browser (Chrome, Firefox, Safari)

**Orders werden nicht ausgeführt:**
- ✅ Prüfe die verfügbare Margin
- ✅ Stelle sicher, dass die Wiedergabe läuft
- ✅ Überprüfe die Position Size

**Performance-Metriken sind falsch:**
- ✅ Warte bis Trades geschlossen sind
- ✅ Reset und teste erneut
- ✅ Prüfe Stop Loss/Take Profit Einstellungen

---

**Viel Erfolg beim Backtesting! 📈💰**
