# Testing Strategy - Trading Backtester

## Overview

This document outlines the comprehensive testing strategy implemented for the refactored trading backtester application. The testing approach focuses on unit testing the custom hooks that contain the core business logic.

## Testing Framework

### Tools Used
- **Vitest**: Fast unit test runner with excellent TypeScript support
- **React Testing Library**: For testing React hooks and components
- **@testing-library/jest-dom**: Additional matchers for DOM testing
- **jsdom**: Browser environment simulation for testing

### Configuration
- **vitest.config.ts**: Main configuration file
- **src/test/setup.ts**: Global test setup and mocks
- **src/test/utils.tsx**: Shared test utilities and mock data

## Test Structure

### 1. Custom Hooks Testing

#### useTradingSession Hook Tests
**File**: `src/hooks/__tests__/useTradingSession.test.ts`

**Test Coverage**:
- ✅ Initial state and computed values
- ✅ Data loading (candle and tick data)
- ✅ Error handling during data loading
- ✅ Timeframe switching
- ✅ Playback controls (start, stop, toggle)
- ✅ Market data updates
- ✅ Computed values (hasData, canStep, totalItems)

**Key Test Scenarios**:
```typescript
// Data loading success
it('should handle data loading successfully', () => {
  const { result } = renderHook(() => useTradingSession());
  act(() => {
    result.current.handleDataLoaded(mockCSVParseResult);
  });
  expect(mockStore.setData).toHaveBeenCalled();
  expect(mockErrorHandler.showSuccess).toHaveBeenCalled();
});

// Error handling
it('should handle errors during data loading', () => {
  const invalidResult = { ...mockCSVParseResult, candleData: [] };
  act(() => {
    result.current.handleDataLoaded(invalidResult);
  });
  expect(mockErrorHandler.handleSyncError).toHaveBeenCalledWith(
    expect.any(Function),
    'Data Loading Failed'
  );
});
```

#### useAccount Hook Tests
**File**: `src/hooks/__tests__/useAccount.test.ts`

**Test Coverage**:
- ✅ Order placement (market and pending orders)
- ✅ Order validation and error handling
- ✅ Position management (close, update levels)
- ✅ Stop loss and take profit validation
- ✅ Pending order triggers
- ✅ Performance metrics calculation
- ✅ Account state updates

**Key Test Scenarios**:
```typescript
// Order placement
it('should place a market order successfully', () => {
  const orderParams = {
    type: 'buy' as const,
    size: 0.1,
    currentBid: 1.1300,
    currentAsk: 1.1302,
    precision: 5,
  };
  act(() => {
    const order = result.current.placeOrder(orderParams);
    expect(order).toBeDefined();
  });
  expect(mockStore.processOrderExecution).toHaveBeenCalled();
});

// Error handling
it('should handle order placement errors', () => {
  canExecuteOrder.mockReturnValue(false);
  const order = result.current.placeOrder(largeOrderParams);
  expect(mockErrorHandler.handleSyncError).toHaveBeenCalledWith(
    expect.any(Function),
    'Order Placement Failed'
  );
});
```

#### useChartInteraction Hook Tests
**File**: `src/hooks/__tests__/useChartInteraction.test.ts`

**Test Coverage**:
- ✅ Chart reference management
- ✅ Context menu handling
- ✅ Drag and drop interactions
- ✅ Fibonacci and trend line management
- ✅ Drawing mode toggles
- ✅ Event handler setup and cleanup

**Key Test Scenarios**:
```typescript
// Context menu actions
it('should handle context menu actions', () => {
  act(() => {
    result.current.handleContextMenuAction('buy', 1.1300, Date.now());
  });
  expect(mockStore.hideContextMenu).toHaveBeenCalled();
  expect(mockOnChartOrderPlace).toHaveBeenCalledWith({
    orderExecutionType: 'market',
    orderType: 'buy',
    price: 1.1300,
    time: expect.any(Number),
    size: 0.1,
  });
});

// Drag and drop
it('should handle mouse events for drag and drop', () => {
  act(() => {
    result.current.handleMouseDown(mockParam);
  });
  expect(mockDragCallbacks.onDragStart).toHaveBeenCalled();
  expect(mockStore.startDrag).toHaveBeenCalled();
});
```

### 2. Test Utilities

#### Mock Data
**File**: `src/test/utils.tsx`

**Provides**:
- Mock candle and tick data
- Mock orders and positions
- Mock account state
- Mock CSV parse results
- Helper functions for testing

#### Store Mocking
```typescript
// Mock store creation
export const createMockStore = <T>(initialState: T) => {
  let state = initialState;
  const listeners = new Set<() => void>();
  
  return {
    getState: () => state,
    setState: (newState: Partial<T>) => {
      state = { ...state, ...newState };
      listeners.forEach(listener => listener());
    },
    subscribe: (listener: () => void) => {
      listeners.add(listener);
      return () => listeners.delete(listener);
    },
  };
};
```

### 3. Mocking Strategy

#### External Dependencies
- **lightweight-charts**: Mocked to avoid canvas dependencies
- **next/navigation**: Mocked for routing functionality
- **Trading calculations**: Mocked with predictable return values

#### Store Mocking
- Each hook test mocks its corresponding Zustand store
- Error handler is mocked to test error scenarios
- Mock functions track calls and return values

## Test Execution

### Running Tests
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with UI
npm run test:ui

# Run tests with coverage
npm run test:coverage
```

### Test Coverage Goals
- **Hooks**: 90%+ coverage for all custom hooks
- **Business Logic**: 100% coverage for critical trading functions
- **Error Handling**: All error paths tested
- **Edge Cases**: Boundary conditions and invalid inputs

## Benefits of Current Testing Strategy

### 1. Isolated Testing
- Each hook is tested in isolation with mocked dependencies
- Business logic is separated from UI concerns
- Easy to identify and fix issues

### 2. Comprehensive Coverage
- All major user flows are tested
- Error scenarios are covered
- Edge cases and boundary conditions

### 3. Fast Execution
- Unit tests run quickly (< 1 second)
- No external dependencies or network calls
- Parallel test execution

### 4. Maintainable Tests
- Clear test structure and naming
- Reusable mock data and utilities
- Easy to add new test cases

## Future Testing Enhancements

### 1. Integration Tests
```typescript
// Test store interactions
describe('Store Integration', () => {
  it('should handle order placement workflow', () => {
    // Test complete workflow from order placement to position creation
  });
});
```

### 2. Component Testing
```typescript
// Test React components
describe('TradingChart Component', () => {
  it('should render chart with data', () => {
    render(<TradingChart data={mockData} />);
    expect(screen.getByRole('chart')).toBeInTheDocument();
  });
});
```

### 3. E2E Testing
```typescript
// Test complete user workflows
describe('Trading Workflow E2E', () => {
  it('should complete full trading session', () => {
    // Test from data import to order execution
  });
});
```

### 4. Performance Testing
```typescript
// Test performance with large datasets
describe('Performance Tests', () => {
  it('should handle large tick datasets efficiently', () => {
    const largeTicks = generateMockTicks(100000);
    // Test performance metrics
  });
});
```

## Continuous Integration

### GitHub Actions (Future)
```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npm test
      - run: npm run test:coverage
```

## Conclusion

The current testing strategy provides a solid foundation for maintaining code quality and preventing regressions. The focus on testing custom hooks ensures that the core business logic is thoroughly validated, while the modular architecture makes testing straightforward and maintainable.

Key achievements:
- ✅ Comprehensive unit test coverage for custom hooks
- ✅ Isolated testing with proper mocking
- ✅ Fast test execution and feedback
- ✅ Clear test structure and documentation
- ✅ Error handling and edge case coverage

This testing approach supports the refactored architecture and provides confidence in the application's reliability and maintainability.
