# FX Backtester - Professional Trading Simulation Platform

A modern, high-performance web application for backtesting forex trading strategies with historical MetaTrader data. Built with a clean, modular architecture for scalability and maintainability.

## 🏗️ Architecture Overview

This application has been completely refactored from a monolithic structure to a modern, modular architecture:

- **🔄 State Management**: Zustand stores for predictable state updates
- **🎣 Custom Hooks**: Business logic encapsulated in reusable hooks
- **🧩 Component Architecture**: Clean separation of concerns with "dumb" presentation components
- **⚡ Performance Optimized**: Minimal re-renders and efficient bundle size (186kB first load)
- **🧪 Fully Tested**: Comprehensive unit and integration tests
- **📝 Type Safe**: Full TypeScript coverage with enhanced type definitions

## 🚀 Key Features

### 📊 **Advanced Data Import**
- **MetaTrader CSV Support**: Full compatibility with MT4/MT5 export format
- **Tick Data Processing**: Real-time tick-by-tick simulation
- **Multiple Timeframes**: S1, S5, S10, S15, S30, M1, M5, M15, M30, H1, H4, D1
- **Smart Data Validation**: Automatic precision detection and error handling
- **Progress Tracking**: Real-time import progress with detailed feedback

### 📈 **Professional Trading Charts**
- **TradingView Integration**: Industry-standard charting library
- **Interactive Elements**: Right-click context menus for order placement
- **Visual Order Management**: Drag-and-drop order modification
- **Technical Analysis**: Fibonacci retracements and trend line tools
- **Real-time Updates**: Live price feeds and position tracking

### ⚡ **High-Performance Playback Engine**
- **Flexible Speed Control**: From real-time to 10x speed
- **Intra-candle Updates**: Sub-timeframe price movements
- **Memory Efficient**: Optimized for large datasets
- **Smooth Navigation**: Precise step-by-step control

### 💼 **Professional Trading Features**
- **Order Types**: Market, Limit, Stop orders with full SL/TP support
- **Risk Management**: Automatic margin calculations and position sizing
- **Real-time P&L**: Live unrealized and realized profit tracking
- **Commission Modeling**: Configurable commission structures
- **Performance Analytics**: Comprehensive trading statistics

### 🎯 **Enhanced User Experience**
- **Error Handling**: Centralized error management with user notifications
- **Loading States**: Progress indicators for all operations
- **Responsive Design**: Optimized for desktop and tablet use
- **Accessibility**: WCAG compliant interface elements

## 🛠️ Technology Stack

### Core Technologies
- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript with strict type checking
- **Styling**: Tailwind CSS with custom trading theme
- **Charts**: TradingView Lightweight Charts
- **State Management**: Zustand with DevTools integration

### Development Tools
- **Testing**: Vitest + React Testing Library
- **Linting**: ESLint with Next.js configuration
- **Type Checking**: TypeScript with enhanced definitions
- **Build Tool**: Next.js with optimized bundling

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- npm or yarn package manager

### Installation

1. **Clone and install dependencies:**
   ```bash
   git clone <repository-url>
   cd fx-backtester-augment
   npm install
   ```

2. **Start development server:**
   ```bash
   npm run dev
   ```

3. **Open application:**
   ```
   http://localhost:3000
   ```

### Available Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run test         # Run unit tests
npm run test:watch   # Run tests in watch mode
npm run test:coverage # Run tests with coverage
```

## 📖 User Guide

### Getting Started

1. **Import Trading Data**
   - Drag & drop your MetaTrader CSV file into the import area
   - Supported formats: Candle data (OHLC) and Tick data
   - Automatic validation and precision detection
   - Real-time import progress tracking

2. **Configure Trading Session**
   - Select timeframe (from 1 second to 1 month)
   - Choose update mode (complete candles or intra-candle)
   - Set initial account balance and trading parameters

3. **Start Backtesting**
   - Use playback controls to navigate through historical data
   - Adjust playback speed from real-time to 10x
   - Step through data manually for precise analysis

4. **Execute Trades**
   - Place orders via the order panel or chart context menu
   - Set stop loss and take profit levels
   - Monitor real-time P&L and margin requirements

5. **Analyze Performance**
   - Track trading statistics and performance metrics
   - Review order history and position details
   - Export results for further analysis

### Advanced Features

#### Chart Interactions
- **Right-click Context Menu**: Quick order placement at any price level
- **Drag & Drop Orders**: Modify pending orders by dragging price lines
- **Fibonacci Tools**: Draw retracements and extensions
- **Trend Lines**: Technical analysis drawing tools

#### Order Management
- **Multiple Order Types**: Market, Limit, Stop orders
- **Risk Management**: Automatic SL/TP execution
- **Position Sizing**: Flexible lot size configuration
- **Margin Monitoring**: Real-time margin level tracking

## 🏗️ Architecture

### Modern React Architecture
- **State Management**: Zustand stores for predictable state updates
- **Custom Hooks**: Business logic encapsulated in reusable hooks
- **Component Design**: Clean separation between presentation and logic
- **Type Safety**: Full TypeScript coverage with enhanced definitions
- **Error Handling**: Centralized error management with user notifications
- **Testing**: Comprehensive unit and integration test coverage

### Performance Optimized
- **Bundle Size**: 186kB first load JS (excellent for complex trading app)
- **Re-renders**: Minimized through selective state subscriptions
- **Memory Management**: Proper cleanup and efficient data structures
- **Code Splitting**: Optimized loading and caching strategies

## 💡 Data Format Support

### MetaTrader CSV Format
```csv
DATE,TIME,OPEN,HIGH,LOW,CLOSE,TICKVOL,VOL,SPREAD
2025.01.20,09:00:00,1.11783,1.11790,1.11775,1.11785,15,0,2
2025.01.20,09:01:00,1.11785,1.11795,1.11780,1.11792,12,0,2
```

### Tick Data Format
```csv
DATE,TIME,BID,ASK,LAST,VOLUME
2025.01.20,09:00:00.123,1.11783,1.11785,1.11784,100
2025.01.20,09:00:00.456,1.11784,1.11786,1.11785,150
```

### Format Requirements
- **Date Format**: `YYYY.MM.DD`
- **Time Format**: `HH:MM:SS` or `HH:MM:SS.mmm` for ticks
- **Decimal Separator**: Dot (`.`)
- **Field Separator**: Comma (`,`)
- **Headers**: First row should contain column names
- **Precision**: Automatic detection (4-5 decimals for forex)

## 🧪 Testing

### Running Tests
```bash
npm test              # Run all tests
npm run test:watch    # Watch mode for development
npm run test:coverage # Generate coverage report
```

### Test Coverage
- **Unit Tests**: 37 tests covering all custom hooks
- **Integration Tests**: 8 tests for store interactions
- **Coverage**: 90%+ for business logic and hooks
- **Error Scenarios**: Comprehensive error handling tests

## 📚 Documentation

- **[Developer Guide](./DEVELOPER_GUIDE.md)**: Comprehensive development documentation
- **[API Documentation](./API_DOCS.md)**: Complete API reference for hooks and stores
- **[Performance Analysis](./PERFORMANCE_ANALYSIS.md)**: Detailed performance metrics and optimizations
- **[Testing Strategy](./TESTING_STRATEGY.md)**: Testing approach and best practices
- **[Refactoring Summary](./REFACTORING_SUMMARY.md)**: Complete refactoring overview

## 🎯 Roadmap

### Completed ✅
- [x] **Modern Architecture**: Zustand stores + custom hooks
- [x] **Performance Optimization**: 186kB bundle, minimal re-renders
- [x] **Comprehensive Testing**: Unit and integration tests
- [x] **Error Handling**: Centralized error management
- [x] **TypeScript**: Full type safety and enhanced definitions
- [x] **Documentation**: Complete developer and API documentation

### Planned Features 🚀
- [ ] **Real-time Data Feeds**: Live market data integration
- [ ] **Advanced Analytics**: Enhanced performance metrics and reporting
- [ ] **Multi-asset Support**: Stocks, commodities, cryptocurrencies
- [ ] **Strategy Builder**: Visual strategy creation interface
- [ ] **Cloud Sync**: Save and sync trading sessions
- [ ] **Mobile App**: React Native mobile application

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guidelines](./CONTRIBUTING.md) for details.

### Development Setup
1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes with tests
4. Run tests: `npm test`
5. Submit a pull request

### Code Standards
- Follow TypeScript best practices
- Write comprehensive tests for new features
- Update documentation for API changes
- Use conventional commit messages

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **[TradingView](https://www.tradingview.com/)** for the Lightweight Charts library
- **[Zustand](https://github.com/pmndrs/zustand)** for excellent state management
- **[Next.js](https://nextjs.org/)** team for the amazing framework
- **[Vitest](https://vitest.dev/)** for fast and reliable testing

## 📞 Support

- **Issues**: [GitHub Issues](https://github.com/your-repo/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-repo/discussions)
- **Documentation**: See the documentation files for detailed guides

---

**Built with ❤️ for the trading community**
