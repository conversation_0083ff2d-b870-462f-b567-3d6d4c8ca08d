// Error Management Store using Zustand

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

export interface AppError {
  id: string;
  type: 'error' | 'warning' | 'info' | 'success';
  title: string;
  message: string;
  timestamp: number;
  duration?: number; // Auto-dismiss after this many milliseconds
  action?: {
    label: string;
    handler: () => void;
  };
}

export interface LoadingState {
  id: string;
  message: string;
  progress?: number; // 0-100 for progress bars
}

interface ErrorStoreState {
  errors: AppError[];
  loadingStates: LoadingState[];
  isGlobalLoading: boolean;
}

interface ErrorStoreActions {
  // Error management
  addError: (error: Omit<AppError, 'id' | 'timestamp'>) => string;
  removeError: (id: string) => void;
  clearErrors: () => void;
  
  // Loading state management
  startLoading: (id: string, message: string, progress?: number) => void;
  updateLoading: (id: string, message?: string, progress?: number) => void;
  stopLoading: (id: string) => void;
  clearAllLoading: () => void;
  
  // Global loading
  setGlobalLoading: (loading: boolean) => void;
  
  // Convenience methods
  showSuccess: (title: string, message: string, duration?: number) => string;
  showError: (title: string, message: string, action?: AppError['action']) => string;
  showWarning: (title: string, message: string, duration?: number) => string;
  showInfo: (title: string, message: string, duration?: number) => string;
}

type ErrorStore = ErrorStoreState & ErrorStoreActions;

const generateId = () => `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

export const useErrorStore = create<ErrorStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      errors: [],
      loadingStates: [],
      isGlobalLoading: false,

      // Error management actions
      addError: (errorData) => {
        const id = generateId();
        const error: AppError = {
          ...errorData,
          id,
          timestamp: Date.now()
        };

        set(
          (state) => ({
            ...state,
            errors: [...state.errors, error]
          }),
          false,
          'addError'
        );

        // Auto-dismiss if duration is specified
        if (error.duration) {
          setTimeout(() => {
            get().removeError(id);
          }, error.duration);
        }

        return id;
      },

      removeError: (id) => {
        set(
          (state) => ({
            ...state,
            errors: state.errors.filter(error => error.id !== id)
          }),
          false,
          'removeError'
        );
      },

      clearErrors: () => {
        set(
          (state) => ({ ...state, errors: [] }),
          false,
          'clearErrors'
        );
      },

      // Loading state actions
      startLoading: (id, message, progress) => {
        const loadingState: LoadingState = {
          id,
          message,
          progress
        };

        set(
          (state) => ({
            ...state,
            loadingStates: [...state.loadingStates.filter(ls => ls.id !== id), loadingState]
          }),
          false,
          'startLoading'
        );
      },

      updateLoading: (id, message, progress) => {
        set(
          (state) => ({
            ...state,
            loadingStates: state.loadingStates.map(ls =>
              ls.id === id
                ? { ...ls, ...(message && { message }), ...(progress !== undefined && { progress }) }
                : ls
            )
          }),
          false,
          'updateLoading'
        );
      },

      stopLoading: (id) => {
        set(
          (state) => ({
            ...state,
            loadingStates: state.loadingStates.filter(ls => ls.id !== id)
          }),
          false,
          'stopLoading'
        );
      },

      clearAllLoading: () => {
        set(
          (state) => ({ ...state, loadingStates: [] }),
          false,
          'clearAllLoading'
        );
      },

      // Global loading
      setGlobalLoading: (isGlobalLoading) => {
        set(
          (state) => ({ ...state, isGlobalLoading }),
          false,
          'setGlobalLoading'
        );
      },

      // Convenience methods
      showSuccess: (title, message, duration = 5000) => {
        return get().addError({
          type: 'success',
          title,
          message,
          duration
        });
      },

      showError: (title, message, action) => {
        return get().addError({
          type: 'error',
          title,
          message,
          action
        });
      },

      showWarning: (title, message, duration = 8000) => {
        return get().addError({
          type: 'warning',
          title,
          message,
          duration
        });
      },

      showInfo: (title, message, duration = 5000) => {
        return get().addError({
          type: 'info',
          title,
          message,
          duration
        });
      }
    }),
    {
      name: 'error-store',
      enabled: process.env.NODE_ENV === 'development'
    }
  )
);

// Hook for easier error handling
export const useErrorHandler = () => {
  const errorStore = useErrorStore();

  const handleAsyncError = async <T>(
    operation: () => Promise<T>,
    errorTitle: string = 'Operation Failed',
    loadingMessage?: string
  ): Promise<T | null> => {
    const loadingId = loadingMessage ? generateId() : null;

    try {
      if (loadingId && loadingMessage) {
        errorStore.startLoading(loadingId, loadingMessage);
      }

      const result = await operation();

      if (loadingId) {
        errorStore.stopLoading(loadingId);
      }

      return result;
    } catch (error) {
      if (loadingId) {
        errorStore.stopLoading(loadingId);
      }

      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      errorStore.showError(errorTitle, errorMessage);

      return null;
    }
  };

  const handleSyncError = <T>(
    operation: () => T,
    errorTitle: string = 'Operation Failed'
  ): T | null => {
    try {
      return operation();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      errorStore.showError(errorTitle, errorMessage);
      return null;
    }
  };

  return {
    ...errorStore,
    handleAsyncError,
    handleSyncError
  };
};
