// Account Management Store using Zustand

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { AccountStore } from '@/types/stores';
import { createInitialAccountState, generateId } from './utils';
import { Account, Order, Position } from '@/types/trading';
import { updateAccount as updateAccountCalculations } from '@/utils/tradingCalculations';

export const useAccountStore = create<AccountStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      ...createInitialAccountState(),

      // Account management actions
      updateAccount: (accountUpdate: Partial<Account>) => {
        set(
          (state) => ({
            ...state,
            account: { ...state.account, ...accountUpdate }
          }),
          false,
          'updateAccount'
        );
      },

      resetAccount: (balance: number) => {
        set(
          (state) => ({
            ...state,
            account: {
              balance,
              equity: balance,
              margin: 0,
              freeMargin: balance,
              marginLevel: 0,
              totalPnL: 0,
              totalCommission: 0
            },
            orders: [],
            positions: [],
            startBalance: balance
          }),
          false,
          'resetAccount'
        );
      },

      // Order management actions
      addOrder: (order: Order) => {
        const orderWithId = {
          ...order,
          id: order.id || generateId()
        };

        set(
          (state) => ({
            ...state,
            orders: [...state.orders, orderWithId]
          }),
          false,
          'addOrder'
        );
      },

      updateOrder: (orderId: string, updates: Partial<Order>) => {
        set(
          (state) => ({
            ...state,
            orders: state.orders.map(order =>
              order.id === orderId ? { ...order, ...updates } : order
            )
          }),
          false,
          'updateOrder'
        );
      },

      removeOrder: (orderId: string) => {
        set(
          (state) => ({
            ...state,
            orders: state.orders.filter(order => order.id !== orderId)
          }),
          false,
          'removeOrder'
        );
      },

      clearOrders: () => {
        set(
          (state) => ({ ...state, orders: [] }),
          false,
          'clearOrders'
        );
      },

      // Position management actions
      addPosition: (position: Position) => {
        const positionWithId = {
          ...position,
          id: position.id || generateId()
        };

        set(
          (state) => ({
            ...state,
            positions: [...state.positions, positionWithId]
          }),
          false,
          'addPosition'
        );
      },

      updatePosition: (positionId: string, updates: Partial<Position>) => {
        set(
          (state) => ({
            ...state,
            positions: state.positions.map(position =>
              position.id === positionId ? { ...position, ...updates } : position
            )
          }),
          false,
          'updatePosition'
        );
      },

      removePosition: (positionId: string) => {
        set(
          (state) => ({
            ...state,
            positions: state.positions.filter(position => position.id !== positionId)
          }),
          false,
          'removePosition'
        );
      },

      clearPositions: () => {
        set(
          (state) => ({ ...state, positions: [] }),
          false,
          'clearPositions'
        );
      },

      // Complex operations
      processOrderExecution: (order: Order, executionPrice: number) => {
        const state = get();
        
        // Create new position from executed order
        const newPosition: Position = {
          id: generateId(),
          orderId: order.id,
          type: order.type,
          size: order.size,
          openPrice: executionPrice,
          openTime: Date.now(),
          stopLoss: order.stopLoss,
          takeProfit: order.takeProfit,
          unrealizedPnL: 0,
          commission: order.commission || 0,
          swap: 0
        };

        // Update account using existing calculation logic
        const updatedAccount = updateAccountCalculations(
          state.account,
          [...state.positions, newPosition],
          state.orders
        );

        set(
          (state) => ({
            ...state,
            positions: [...state.positions, newPosition],
            orders: state.orders.filter(o => o.id !== order.id),
            account: updatedAccount
          }),
          false,
          'processOrderExecution'
        );
      },

      updatePositionPnL: (positionId: string, currentPrice: number) => {
        const state = get();
        const position = state.positions.find(p => p.id === positionId);
        
        if (!position) return;

        // Calculate unrealized PnL
        const priceDiff = position.type === 'buy' 
          ? currentPrice - position.openPrice
          : position.openPrice - currentPrice;
        const unrealizedPnL = priceDiff * position.size * 100000; // Assuming forex lot size

        // Update position
        const updatedPositions = state.positions.map(p =>
          p.id === positionId ? { ...p, unrealizedPnL } : p
        );

        // Recalculate account
        const updatedAccount = updateAccountCalculations(
          state.account,
          updatedPositions,
          state.orders
        );

        set(
          (state) => ({
            ...state,
            positions: updatedPositions,
            account: updatedAccount
          }),
          false,
          'updatePositionPnL'
        );
      }
    }),
    {
      name: 'account-store',
      enabled: process.env.NODE_ENV === 'development'
    }
  )
);
