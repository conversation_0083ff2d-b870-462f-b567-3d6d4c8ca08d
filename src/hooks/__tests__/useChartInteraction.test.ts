// Unit tests for useChartInteraction hook
import { renderHook, act } from '@testing-library/react';
import { useChartInteraction } from '../useChartInteraction';
import { useChartInteractionStore } from '@/stores';

// Mock the stores
vi.mock('@/stores', () => ({
  useChartInteractionStore: vi.fn(),
}));

describe('useChartInteraction', () => {
  const mockStore = {
    // State
    fibonacciLines: [],
    trendLines: [],
    isDragging: false,
    draggedOrderId: undefined,
    draggedPositionId: undefined,
    contextMenu: {
      visible: false,
      x: 0,
      y: 0,
      price: 0,
      time: 0,
    },
    enableFibonacci: true,
    enableDragAndDrop: true,

    // Actions
    addFibonacciLine: vi.fn(),
    removeFibonacciLine: vi.fn(),
    clearFibonacciLines: vi.fn(),
    addTrendLine: vi.fn(),
    removeTrendLine: vi.fn(),
    clearTrendLines: vi.fn(),
    startDrag: vi.fn(),
    endDrag: vi.fn(),
    showContextMenu: vi.fn(),
    hideContextMenu: vi.fn(),
    setEnableFibonacci: vi.fn(),
    setEnableDragAndDrop: vi.fn(),
  };

  const mockOnChartOrderPlace = vi.fn();
  const mockDragCallbacks = {
    onDragStart: vi.fn(),
    onDragEnd: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (useChartInteractionStore as any).mockReturnValue(mockStore);
  });

  it('should return initial state and actions', () => {
    const { result } = renderHook(() => useChartInteraction({
      onChartOrderPlace: mockOnChartOrderPlace,
      dragCallbacks: mockDragCallbacks,
    }));

    expect(result.current.fibonacciLines).toEqual([]);
    expect(result.current.trendLines).toEqual([]);
    expect(result.current.isDragging).toBe(false);
    expect(result.current.enableFibonacci).toBe(true);
    expect(result.current.enableDragAndDrop).toBe(true);
  });

  it('should handle chart reference management', () => {
    const { result } = renderHook(() => useChartInteraction());
    const mockChart = { mock: 'chart' };

    act(() => {
      result.current.setChartRef(mockChart);
    });

    expect(result.current.chartRef).toBe(mockChart);
  });

  it('should handle context menu actions', () => {
    const { result } = renderHook(() => useChartInteraction({
      onChartOrderPlace: mockOnChartOrderPlace,
    }));

    act(() => {
      result.current.handleContextMenuAction('buy', 1.1300, Date.now());
    });

    expect(mockStore.hideContextMenu).toHaveBeenCalled();
    expect(mockOnChartOrderPlace).toHaveBeenCalledWith({
      orderExecutionType: 'market',
      orderType: 'buy',
      price: 1.1300,
      time: expect.any(Number),
      size: 0.1,
    });
  });

  it('should handle different order types in context menu', () => {
    const { result } = renderHook(() => useChartInteraction({
      onChartOrderPlace: mockOnChartOrderPlace,
    }));

    const orderTypes = ['buy', 'sell', 'buy-limit', 'sell-limit', 'buy-stop', 'sell-stop'];

    orderTypes.forEach(orderType => {
      act(() => {
        result.current.handleContextMenuAction(orderType, 1.1300, Date.now());
      });

      expect(mockOnChartOrderPlace).toHaveBeenCalledWith({
        orderExecutionType: 'market',
        orderType,
        price: 1.1300,
        time: expect.any(Number),
        size: 0.1,
      });
    });
  });

  it('should handle mouse events for drag and drop', () => {
    const { result } = renderHook(() => useChartInteraction({
      dragCallbacks: mockDragCallbacks,
    }));

    const mockParam = {
      point: { x: 100, y: 200 },
      seriesData: new Map([
        ['series1', { value: 1.1300 }]
      ])
    };

    // Test mouse down
    act(() => {
      result.current.handleMouseDown(mockParam);
    });

    expect(mockDragCallbacks.onDragStart).toHaveBeenCalledWith('unknown', 'pending_order');
    expect(mockStore.startDrag).toHaveBeenCalled();

    // Test mouse move
    act(() => {
      result.current.handleMouseMove(mockParam);
    });

    // Test mouse up
    act(() => {
      result.current.handleMouseUp(mockParam);
    });

    expect(mockDragCallbacks.onDragEnd).toHaveBeenCalledWith('unknown', 'pending_order', true);
    expect(mockStore.endDrag).toHaveBeenCalled();
  });

  it('should handle Fibonacci line management', () => {
    const { result } = renderHook(() => useChartInteraction());

    act(() => {
      result.current.addFibonacciRetracement(1.1300, 1.1400, Date.now(), Date.now() + 1000);
    });

    expect(mockStore.addFibonacciLine).toHaveBeenCalledWith(
      1.1300,
      1.1400,
      expect.any(Number),
      expect.any(Number)
    );

    act(() => {
      result.current.clearAllDrawings();
    });

    expect(mockStore.clearFibonacciLines).toHaveBeenCalled();
    expect(mockStore.clearTrendLines).toHaveBeenCalled();
  });

  it('should handle trend line management', () => {
    const { result } = renderHook(() => useChartInteraction());

    act(() => {
      result.current.addTrendLine(1.1300, 1.1400, Date.now(), Date.now() + 1000);
    });

    expect(mockStore.addTrendLine).toHaveBeenCalledWith(
      1.1300,
      1.1400,
      expect.any(Number),
      expect.any(Number)
    );
  });

  it('should handle drawing mode toggles', () => {
    const { result } = renderHook(() => useChartInteraction());

    act(() => {
      result.current.startFibonacciDrawing();
    });

    // Should log to console (mocked behavior)
    expect(console.log).toHaveBeenCalledWith('Starting Fibonacci drawing mode');

    act(() => {
      result.current.startTrendLineDrawing();
    });

    expect(console.log).toHaveBeenCalledWith('Starting trend line drawing mode');
  });

  it('should handle chart event handlers setup', () => {
    const { result } = renderHook(() => useChartInteraction());
    const mockChart = {
      subscribeClick: vi.fn(),
      subscribeCrosshairMove: vi.fn(),
      unsubscribeClick: vi.fn(),
      unsubscribeCrosshairMove: vi.fn(),
    };

    act(() => {
      const cleanup = result.current.setupChartEventHandlers(mockChart);
      expect(mockChart.subscribeClick).toHaveBeenCalled();
      expect(mockChart.subscribeCrosshairMove).toHaveBeenCalled();

      // Test cleanup
      cleanup();
      expect(mockChart.unsubscribeClick).toHaveBeenCalled();
      expect(mockChart.unsubscribeCrosshairMove).toHaveBeenCalled();
    });
  });

  it('should handle coordinate conversion utilities', () => {
    const { result } = renderHook(() => useChartInteraction());

    // These methods return null in the simplified implementation
    const price = result.current.coordinateToPrice(100);
    expect(price).toBeNull();

    const coordinate = result.current.priceToCoordinate(1.1300);
    expect(coordinate).toBeNull();
  });

  it('should handle context menu without order placement callback', () => {
    const { result } = renderHook(() => useChartInteraction()); // No callback provided

    act(() => {
      result.current.handleContextMenuAction('buy', 1.1300, Date.now());
    });

    expect(mockStore.hideContextMenu).toHaveBeenCalled();
    // Should not throw error even without callback
  });

  it('should handle mouse events without drag callbacks', () => {
    const { result } = renderHook(() => useChartInteraction()); // No drag callbacks

    const mockParam = {
      point: { x: 100, y: 200 },
      seriesData: new Map([
        ['series1', { value: 1.1300 }]
      ])
    };

    // Should not throw errors even without callbacks
    act(() => {
      result.current.handleMouseDown(mockParam);
      result.current.handleMouseMove(mockParam);
      result.current.handleMouseUp(mockParam);
    });

    expect(mockStore.startDrag).toHaveBeenCalled();
    expect(mockStore.endDrag).toHaveBeenCalled();
  });
});
