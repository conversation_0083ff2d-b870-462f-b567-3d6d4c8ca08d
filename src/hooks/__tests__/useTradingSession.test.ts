// Unit tests for useTradingSession hook
import { renderHook, act } from '@testing-library/react';
import { useTradingSession } from '../useTradingSession';
import { useTradingSessionStore } from '@/stores';
import { useErrorHandler } from '@/stores/errorStore';
import { mockCandleData, mockTickData, mockCSVParseResult } from '@/test/utils';

// Mock the stores
vi.mock('@/stores', () => ({
  useTradingSessionStore: vi.fn(),
}));

vi.mock('@/stores/errorStore', () => ({
  useErrorHandler: vi.fn(),
}));

vi.mock('@/utils/timeframeAggregator', () => ({
  TimeframeAggregator: vi.fn().mockImplementation(() => ({
    aggregateToTimeframe: vi.fn().mockReturnValue(mockCandleData),
  })),
}));

vi.mock('@/utils/tradingCalculations', () => ({
  getBidAskFromCandle: vi.fn().mockReturnValue({
    bid: 1.1300,
    ask: 1.1302,
    spread: 0.0002,
  }),
  getBidAskFromTick: vi.fn().mockReturnValue({
    bid: 1.1300,
    ask: 1.1302,
    spread: 0.0002,
  }),
}));

describe('useTradingSession', () => {
  const mockStore = {
    // State
    symbol: 'EURUSD',
    data: mockCandleData,
    baseData: mockCandleData,
    tickData: undefined,
    dataType: 'candle' as const,
    currentIndex: 0,
    isPlaying: false,
    playbackSpeed: 1000,
    currentBid: 1.1300,
    currentAsk: 1.1302,
    spread: 0.0002,
    lastKnownBid: 1.1300,
    lastKnownAsk: 1.1302,
    precision: 5,
    timeframe: 'M1' as const,
    updateMode: 'complete' as const,
    settings: {
      commissionPerLot: 5,
      leverage: 100,
      marginRequirement: 0.01,
    },
    showImporter: false,
    showSettings: false,

    // Actions
    setData: vi.fn(),
    setSymbol: vi.fn(),
    setDataType: vi.fn(),
    setPrecision: vi.fn(),
    setCurrentIndex: vi.fn(),
    setIsPlaying: vi.fn(),
    setPlaybackSpeed: vi.fn(),
    stepForward: vi.fn(),
    stepBackward: vi.fn(),
    resetSession: vi.fn(),
    updateMarketData: vi.fn(),
    setLastKnownPrices: vi.fn(),
    setTimeframe: vi.fn(),
    setUpdateMode: vi.fn(),
    updateSettings: vi.fn(),
    setShowImporter: vi.fn(),
    setShowSettings: vi.fn(),
  };

  const mockErrorHandler = {
    handleSyncError: vi.fn((fn) => fn()),
    startLoading: vi.fn(),
    updateLoading: vi.fn(),
    stopLoading: vi.fn(),
    showSuccess: vi.fn(),
    showError: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (useTradingSessionStore as any).mockReturnValue(mockStore);
    (useErrorHandler as any).mockReturnValue(mockErrorHandler);
  });

  it('should return initial state and actions', () => {
    const { result } = renderHook(() => useTradingSession());

    expect(result.current.symbol).toBe('EURUSD');
    expect(result.current.data).toEqual(mockCandleData);
    expect(result.current.currentIndex).toBe(0);
    expect(result.current.isPlaying).toBe(false);
    expect(result.current.hasData).toBe(true);
    expect(result.current.totalItems).toBe(3);
    expect(result.current.canStepForward).toBe(true);
    expect(result.current.canStepBackward).toBe(false);
  });

  it('should handle data loading successfully', () => {
    const { result } = renderHook(() => useTradingSession());

    act(() => {
      result.current.handleDataLoaded(mockCSVParseResult);
    });

    expect(mockErrorHandler.handleSyncError).toHaveBeenCalled();
    expect(mockErrorHandler.startLoading).toHaveBeenCalledWith('data-processing', 'Processing data...');
    expect(mockStore.setData).toHaveBeenCalledWith(mockCandleData, mockCandleData, undefined);
    expect(mockStore.setSymbol).toHaveBeenCalledWith('EURUSD');
    expect(mockStore.setDataType).toHaveBeenCalledWith('candle');
    expect(mockStore.setPrecision).toHaveBeenCalledWith(5);
    expect(mockStore.setShowImporter).toHaveBeenCalledWith(false);
    expect(mockErrorHandler.stopLoading).toHaveBeenCalledWith('data-processing');
    expect(mockErrorHandler.showSuccess).toHaveBeenCalled();
  });

  it('should handle tick data loading', () => {
    const { result } = renderHook(() => useTradingSession());
    const tickDataResult = {
      ...mockCSVParseResult,
      dataType: 'tick' as const,
      candleData: undefined,
      tickData: mockTickData,
    };

    act(() => {
      result.current.handleDataLoaded(tickDataResult);
    });

    expect(mockErrorHandler.updateLoading).toHaveBeenCalledWith('data-processing', 'Aggregating tick data to candles...', 25);
    expect(mockErrorHandler.updateLoading).toHaveBeenCalledWith('data-processing', 'Generating M1 candles...', 50);
    expect(mockStore.setData).toHaveBeenCalled();
    expect(mockStore.setPlaybackSpeed).toHaveBeenCalledWith(100); // Faster for tick data
  });

  it('should handle timeframe switching', () => {
    const { result } = renderHook(() => useTradingSession());

    act(() => {
      result.current.switchTimeframe('M5');
    });

    expect(mockStore.setTimeframe).toHaveBeenCalledWith('M5');
  });

  it('should handle playback controls', () => {
    const { result } = renderHook(() => useTradingSession());

    // Test start playback
    act(() => {
      result.current.startPlayback();
    });

    expect(mockStore.setIsPlaying).toHaveBeenCalledWith(true);

    // Test stop playback
    act(() => {
      result.current.stopPlayback();
    });

    expect(mockStore.setIsPlaying).toHaveBeenCalledWith(false);

    // Test toggle playback
    act(() => {
      result.current.togglePlayback();
    });

    // Should call startPlayback since isPlaying is false
    expect(mockStore.setIsPlaying).toHaveBeenCalledWith(true);
  });

  it('should calculate correct computed values', () => {
    // Test with no data
    const emptyStore = { ...mockStore, data: [], tickData: undefined };
    (useTradingSessionStore as any).mockReturnValue(emptyStore);

    const { result: emptyResult } = renderHook(() => useTradingSession());
    expect(emptyResult.current.hasData).toBe(false);
    expect(emptyResult.current.totalItems).toBe(0);
    expect(emptyResult.current.canStepForward).toBe(false);
    expect(emptyResult.current.canStepBackward).toBe(false);

    // Test with data at last index
    const lastIndexStore = { ...mockStore, currentIndex: 2 }; // Last index for 3 items
    (useTradingSessionStore as any).mockReturnValue(lastIndexStore);

    const { result: lastResult } = renderHook(() => useTradingSession());
    expect(lastResult.current.canStepForward).toBe(false);
    expect(lastResult.current.canStepBackward).toBe(true);
  });

  it('should handle errors during data loading', () => {
    const { result } = renderHook(() => useTradingSession());
    const invalidResult = {
      ...mockCSVParseResult,
      candleData: [], // Empty data should trigger error
    };

    mockErrorHandler.handleSyncError.mockImplementation((fn, errorTitle) => {
      try {
        return fn();
      } catch (error) {
        // Simulate error handling
        return null;
      }
    });

    act(() => {
      result.current.handleDataLoaded(invalidResult);
    });

    expect(mockErrorHandler.handleSyncError).toHaveBeenCalledWith(
      expect.any(Function),
      'Data Loading Failed'
    );
  });

  it('should update market data when current index changes', () => {
    const { result } = renderHook(() => useTradingSession());

    // Market data updates are now handled automatically via useEffect
    // when currentIndex changes, so we just verify the hook works
    expect(result.current.currentBid).toBeDefined();
    expect(result.current.currentAsk).toBeDefined();
    expect(result.current.spread).toBeDefined();
  });
});
