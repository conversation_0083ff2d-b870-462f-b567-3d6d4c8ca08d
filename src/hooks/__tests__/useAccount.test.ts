// Unit tests for useAccount hook
import { renderHook, act } from '@testing-library/react';
import { useAccount } from '../useAccount';
import { useAccountStore } from '@/stores';
import { useErrorHandler } from '@/stores/errorStore';
import { mockAccount, mockOrder, mockPosition } from '@/test/utils';

// Mock the stores
vi.mock('@/stores', () => ({
  useAccountStore: vi.fn(),
}));

vi.mock('@/stores/errorStore', () => ({
  useErrorHandler: vi.fn(),
}));

vi.mock('@/utils/tradingCalculations', () => ({
  calculateCommission: vi.fn().mockReturnValue(5),
  canExecuteOrder: vi.fn().mockReturnValue(true),
  validateOrderPrice: vi.fn().mockReturnValue(true),
  getExecutionPrice: vi.fn().mockReturnValue(1.1300),
  shouldTriggerPendingOrder: vi.fn().mockReturnValue(false),
  calculatePerformanceMetrics: vi.fn().mockReturnValue({
    totalTrades: 1,
    winningTrades: 1,
    losingTrades: 0,
    winRate: 100,
    totalPnL: 50,
    maxDrawdown: 0,
    sharpeRatio: 2.5,
    profitFactor: 2.0,
    averageWin: 50,
    averageLoss: 0,
    largestWin: 50,
    largestLoss: 0,
  }),
  checkStopLossTakeProfitBidAsk: vi.fn().mockReturnValue(false),
  calculateUnrealizedPnLBidAsk: vi.fn().mockReturnValue(50),
}));

describe('useAccount', () => {
  const mockStore = {
    // State
    account: mockAccount,
    orders: [mockOrder],
    positions: [mockPosition],
    startBalance: 10000,

    // Actions
    updateAccount: vi.fn(),
    resetAccount: vi.fn(),
    addOrder: vi.fn(),
    updateOrder: vi.fn(),
    removeOrder: vi.fn(),
    clearOrders: vi.fn(),
    addPosition: vi.fn(),
    updatePosition: vi.fn(),
    removePosition: vi.fn(),
    clearPositions: vi.fn(),
    processOrderExecution: vi.fn(),
    updatePositionPnL: vi.fn(),
  };

  const mockErrorHandler = {
    handleSyncError: vi.fn((fn) => fn()),
    showSuccess: vi.fn(),
    showInfo: vi.fn(),
    showError: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (useAccountStore as any).mockReturnValue(mockStore);
    (useErrorHandler as any).mockReturnValue(mockErrorHandler);
  });

  it('should return initial state and computed values', () => {
    const { result } = renderHook(() => useAccount());

    expect(result.current.account).toEqual(mockAccount);
    expect(result.current.orders).toEqual([mockOrder]);
    expect(result.current.positions).toEqual([mockPosition]);
    expect(result.current.startBalance).toBe(10000);
    expect(result.current.totalUnrealizedPnL).toBe(50);
  });

  it('should place a market order successfully', () => {
    const { result } = renderHook(() => useAccount());

    const orderParams = {
      type: 'buy' as const,
      size: 0.1,
      currentBid: 1.1300,
      currentAsk: 1.1302,
      precision: 5,
    };

    act(() => {
      const order = result.current.placeOrder(orderParams);
      expect(order).toBeDefined();
    });

    expect(mockErrorHandler.handleSyncError).toHaveBeenCalled();
    expect(mockStore.processOrderExecution).toHaveBeenCalled();
    expect(mockErrorHandler.showSuccess).toHaveBeenCalledWith(
      'Order Executed',
      expect.stringContaining('BUY 0.1 lots')
    );
  });

  it('should place a pending order successfully', () => {
    const { result } = renderHook(() => useAccount());

    const orderParams = {
      type: 'buy-limit' as const,
      size: 0.1,
      price: 1.1250, // Below current price for buy limit
      currentBid: 1.1300,
      currentAsk: 1.1302,
      precision: 5,
    };

    act(() => {
      const order = result.current.placeOrder(orderParams);
      expect(order).toBeDefined();
    });

    expect(mockStore.addOrder).toHaveBeenCalled();
    expect(mockErrorHandler.showInfo).toHaveBeenCalledWith(
      'Pending Order Placed',
      expect.stringContaining('BUY-LIMIT 0.1 lots')
    );
  });

  it('should handle order placement errors', () => {
    const { result } = renderHook(() => useAccount());

    // Mock insufficient margin
    const { canExecuteOrder } = require('@/utils/tradingCalculations');
    canExecuteOrder.mockReturnValue(false);

    mockErrorHandler.handleSyncError.mockImplementation((fn, errorTitle) => {
      try {
        return fn();
      } catch (error) {
        return {} as any; // Return empty order on error
      }
    });

    const orderParams = {
      type: 'buy' as const,
      size: 10, // Large size to trigger insufficient margin
      currentBid: 1.1300,
      currentAsk: 1.1302,
      precision: 5,
    };

    act(() => {
      const order = result.current.placeOrder(orderParams);
      expect(order).toEqual({});
    });

    expect(mockErrorHandler.handleSyncError).toHaveBeenCalledWith(
      expect.any(Function),
      'Order Placement Failed'
    );
  });

  it('should close position successfully', () => {
    const { result } = renderHook(() => useAccount());

    act(() => {
      result.current.closePosition('test-position-1', 1.1350, 1.1352);
    });

    expect(mockStore.updateAccount).toHaveBeenCalled();
    expect(mockStore.removePosition).toHaveBeenCalledWith('test-position-1');
  });

  it('should update position levels', () => {
    const { result } = renderHook(() => useAccount());

    act(() => {
      result.current.updatePositionLevels(
        'test-position-1',
        1.1200, // New stop loss
        1.1450, // New take profit
        1.1350, // Current bid
        1.1352  // Current ask
      );
    });

    expect(mockStore.updatePosition).toHaveBeenCalledWith('test-position-1', {
      stopLoss: 1.1200,
      takeProfit: 1.1450,
    });
  });

  it('should validate stop loss and take profit levels', () => {
    const { result } = renderHook(() => useAccount());

    // Test invalid stop loss (above current price for buy position)
    expect(() => {
      act(() => {
        result.current.updatePositionLevels(
          'test-position-1',
          1.1400, // Invalid SL above current price for buy
          undefined,
          1.1350, // Current bid
          1.1352  // Current ask
        );
      });
    }).toThrow('Invalid stop loss level');

    // Test invalid take profit (below current price for buy position)
    expect(() => {
      act(() => {
        result.current.updatePositionLevels(
          'test-position-1',
          undefined,
          1.1300, // Invalid TP below current price for buy
          1.1350, // Current bid
          1.1352  // Current ask
        );
      });
    }).toThrow('Invalid take profit level');
  });

  it('should check pending orders', () => {
    const { result } = renderHook(() => useAccount());

    const { shouldTriggerPendingOrder } = require('@/utils/tradingCalculations');
    shouldTriggerPendingOrder.mockReturnValue(true);

    act(() => {
      result.current.checkPendingOrders(1.1300, 1.1302);
    });

    expect(mockStore.processOrderExecution).toHaveBeenCalled();
  });

  it('should check stop loss and take profit triggers', () => {
    const { result } = renderHook(() => useAccount());

    const { checkStopLossTakeProfitBidAsk } = require('@/utils/tradingCalculations');
    checkStopLossTakeProfitBidAsk.mockReturnValue(true);

    act(() => {
      result.current.checkStopLossTakeProfit(1.1200, 1.1202); // Price hits SL
    });

    expect(mockStore.updateAccount).toHaveBeenCalled();
    expect(mockStore.removePosition).toHaveBeenCalled();
  });

  it('should update all position PnLs', () => {
    const { result } = renderHook(() => useAccount());

    act(() => {
      result.current.updateAllPositionPnLs(1.1350, 1.1352);
    });

    expect(mockStore.updatePosition).toHaveBeenCalledWith('test-position-1', {
      unrealizedPnL: 50,
    });
  });

  it('should reset account', () => {
    const { result } = renderHook(() => useAccount());

    act(() => {
      result.current.resetAccount(15000);
    });

    expect(mockStore.resetAccount).toHaveBeenCalledWith(15000);
  });

  it('should calculate performance metrics', () => {
    const { result } = renderHook(() => useAccount());

    const metrics = result.current.performanceMetrics;

    expect(metrics).toEqual({
      totalTrades: 1,
      winningTrades: 1,
      losingTrades: 0,
      winRate: 100,
      totalPnL: 50,
      maxDrawdown: 0,
      sharpeRatio: 2.5,
      profitFactor: 2.0,
      averageWin: 50,
      averageLoss: 0,
      largestWin: 50,
      largestLoss: 0,
    });
  });

  it('should update account with computed values via useEffect', () => {
    const { result } = renderHook(() => useAccount());

    // The useEffect should update the account with computed values
    expect(mockStore.updateAccount).toHaveBeenCalledWith({
      equity: 10050, // balance + unrealized PnL
      margin: 10, // simplified margin calculation
      freeMargin: 10040, // equity - margin
      marginLevel: 1005, // (equity / margin) * 100
      totalCommission: 5,
    });
  });
});
