import { useState, useCallback, useRef, useEffect } from 'react';
import { IPriceLine } from 'lightweight-charts';
import { 
  DragState, 
  LineMetadata, 
  DragLineType, 
  DragCallbacks,
  DragToleranceConfig 
} from '@/types/trading';
import { 
  createInitialDragState, 
  calculateDragDirection,
  DEFAULT_DRAG_TOLERANCE 
} from '@/utils/dragUtils';

export interface UseDragStateOptions {
  tolerance?: Partial<DragToleranceConfig>;
  callbacks?: DragCallbacks;
}

export interface UseDragStateReturn {
  dragState: DragState;
  lineMetadataMap: Map<IPriceLine, LineMetadata>;
  
  // Line metadata management
  addLineMetadata: (line: IPriceLine, metadata: LineMetadata) => void;
  removeLineMetadata: (line: IPriceLine) => void;
  updateLineMetadata: (line: IPriceLine, updates: Partial<LineMetadata>) => void;
  getLineMetadata: (line: IPriceLine) => LineMetadata | undefined;
  clearAllMetadata: () => void;
  
  // Drag state management
  startDrag: (lineId: string, lineType: DragLineType, startPrice: number, startY: number) => void;
  updateDrag: (currentPrice: number, currentY: number, isValid: boolean) => void;
  endDrag: (success: boolean) => void;
  cancelDrag: () => void;
  
  // Utility functions
  isDragging: boolean;
  isLineDragging: (lineId: string) => boolean;
  getDraggedLineMetadata: () => LineMetadata | null;
}

export function useDragState(options: UseDragStateOptions = {}): UseDragStateReturn {
  const { tolerance = {}, callbacks = {} } = options;
  const config = { ...DEFAULT_DRAG_TOLERANCE, ...tolerance };
  
  // State management
  const [dragState, setDragState] = useState<DragState>(createInitialDragState);
  const lineMetadataMapRef = useRef<Map<IPriceLine, LineMetadata>>(new Map());
  
  // Callbacks refs to ensure stable references
  const callbacksRef = useRef<DragCallbacks>(callbacks);
  callbacksRef.current = callbacks;
  
  // Line metadata management functions
  const addLineMetadata = useCallback((line: IPriceLine, metadata: LineMetadata) => {
    lineMetadataMapRef.current.set(line, metadata);
  }, []);
  
  const removeLineMetadata = useCallback((line: IPriceLine) => {
    lineMetadataMapRef.current.delete(line);
  }, []);
  
  const updateLineMetadata = useCallback((line: IPriceLine, updates: Partial<LineMetadata>) => {
    const existing = lineMetadataMapRef.current.get(line);
    if (existing) {
      lineMetadataMapRef.current.set(line, { ...existing, ...updates });
    }
  }, []);
  
  const getLineMetadata = useCallback((line: IPriceLine): LineMetadata | undefined => {
    return lineMetadataMapRef.current.get(line);
  }, []);
  
  const clearAllMetadata = useCallback(() => {
    lineMetadataMapRef.current.clear();
  }, []);
  
  // Drag state management functions
  const startDrag = useCallback((
    lineId: string, 
    lineType: DragLineType, 
    startPrice: number, 
    startY: number
  ) => {
    setDragState({
      isDragging: true,
      draggedLineId: lineId,
      draggedLineType: lineType,
      startPrice,
      currentPrice: startPrice,
      startY,
      currentY: startY,
      isValidPosition: true,
      dragDirection: null
    });
    
    // Call onDragStart callback
    if (callbacksRef.current.onDragStart) {
      callbacksRef.current.onDragStart(lineId, lineType);
    }
  }, []);
  
  const updateDrag = useCallback((currentPrice: number, currentY: number, isValid: boolean) => {
    setDragState(prev => {
      if (!prev.isDragging) return prev;
      
      const dragDirection = calculateDragDirection(
        prev.startY, 
        currentY, 
        config.minDragDistance
      );
      
      const newState = {
        ...prev,
        currentPrice,
        currentY,
        isValidPosition: isValid,
        dragDirection
      };
      
      // Call validation change callback if validity changed
      if (prev.isValidPosition !== isValid && callbacksRef.current.onDragValidationChange) {
        callbacksRef.current.onDragValidationChange(isValid);
      }
      
      return newState;
    });
  }, [config.minDragDistance]);
  
  const endDrag = useCallback((success: boolean) => {
    setDragState(prev => {
      if (!prev.isDragging) return prev;
      
      // Call onDragEnd callback
      if (callbacksRef.current.onDragEnd && prev.draggedLineId && prev.draggedLineType) {
        callbacksRef.current.onDragEnd(prev.draggedLineId, prev.draggedLineType, success);
      }
      
      return createInitialDragState();
    });
  }, []);
  
  const cancelDrag = useCallback(() => {
    endDrag(false);
  }, [endDrag]);
  
  // Utility functions
  const isDragging = dragState.isDragging;
  
  const isLineDragging = useCallback((lineId: string): boolean => {
    return dragState.isDragging && dragState.draggedLineId === lineId;
  }, [dragState.isDragging, dragState.draggedLineId]);
  
  const getDraggedLineMetadata = useCallback((): LineMetadata | null => {
    if (!dragState.isDragging || !dragState.draggedLineId) return null;
    
    // Find the metadata for the currently dragged line
    for (const [, metadata] of lineMetadataMapRef.current.entries()) {
      if (metadata.id === dragState.draggedLineId) {
        return metadata;
      }
    }
    
    return null;
  }, [dragState.isDragging, dragState.draggedLineId]);
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      lineMetadataMapRef.current.clear();
    };
  }, []);
  
  return {
    dragState,
    lineMetadataMap: lineMetadataMapRef.current,
    
    // Line metadata management
    addLineMetadata,
    removeLineMetadata,
    updateLineMetadata,
    getLineMetadata,
    clearAllMetadata,
    
    // Drag state management
    startDrag,
    updateDrag,
    endDrag,
    cancelDrag,
    
    // Utility functions
    isDragging,
    isLineDragging,
    getDraggedLineMetadata
  };
}
