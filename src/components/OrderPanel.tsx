'use client';

import React, { useState } from 'react';
import { TrendingUp, TrendingDown, DollarSign } from 'lucide-react';
import { OrderType } from '@/types/trading';
import { formatPrice, formatCurrency } from '@/utils/tradingCalculations';

interface OrderPanelProps {
  currentPrice: number;
  currentBid?: number;
  currentAsk?: number;
  spread?: number;
  onPlaceOrder: (order: {
    type: OrderType;
    size: number;
    stopLoss?: number;
    takeProfit?: number;
  }) => void;
  canTrade: boolean;
  balance: number;
  freeMargin: number;
  precision?: number;
}

export default function OrderPanel({
  currentPrice,
  currentBid,
  currentAsk,
  spread,
  onPlaceOrder,
  canTrade,
  balance,
  freeMargin,
  precision = 5
}: OrderPanelProps) {
  const [orderType, setOrderType] = useState<OrderType>('buy');
  const [size, setSize] = useState<number>(0.1);
  const [stopLoss, setStopLoss] = useState<string>('');
  const [takeProfit, setTakeProfit] = useState<string>('');
  const [showAdvanced, setShowAdvanced] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!canTrade) return;

    const order = {
      type: orderType,
      size,
      stopLoss: stopLoss ? parseFloat(stopLoss) : undefined,
      takeProfit: takeProfit ? parseFloat(takeProfit) : undefined,
    };

    onPlaceOrder(order);

    // Reset form
    setStopLoss('');
    setTakeProfit('');
  };

  const calculateRequiredMargin = () => {
    // Use execution price for margin calculation
    const executionPrice = orderType === 'buy' 
      ? (currentAsk || currentPrice) 
      : (currentBid || currentPrice);
    return (size * 100000 * executionPrice) / 100; // 1:100 leverage
  };

  const calculatePotentialPnL = (targetPrice: number) => {
    // Use appropriate execution price
    const entryPrice = orderType === 'buy' 
      ? (currentAsk || currentPrice) 
      : (currentBid || currentPrice);
      
    const priceDiff = orderType === 'buy'
      ? targetPrice - entryPrice
      : entryPrice - targetPrice;
    return priceDiff * size * 100000;
  };

  const requiredMargin = calculateRequiredMargin();
  const canAfford = requiredMargin <= freeMargin;

  return (
    <div className="trading-card">
      <h3 className="text-lg font-semibold text-trading-text-primary mb-6 flex items-center">
        <div className="w-2 h-2 bg-trading-info rounded-full mr-2"></div>
        Place Order
      </h3>

      {/* Account info */}
      <div className="grid grid-cols-2 gap-4 mb-6 p-4 bg-trading-surface border border-trading-border rounded-lg">
        <div>
          <div className="text-xs text-trading-text-secondary">Balance</div>
          <div className="text-sm font-semibold text-trading-text-primary">{formatCurrency(balance)}</div>
        </div>
        <div>
          <div className="text-xs text-trading-text-secondary">Free Margin</div>
          <div className="text-sm font-semibold text-trading-text-primary">{formatCurrency(freeMargin)}</div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Order type buttons */}
        <div className="grid grid-cols-2 gap-3">
          <button
            type="button"
            onClick={() => setOrderType('buy')}
            className={`
              flex items-center justify-center px-4 py-4 rounded-lg font-semibold transition-all duration-200 border-2
              ${orderType === 'buy'
                ? 'bg-trading-success text-white border-trading-success shadow-lg scale-105'
                : 'bg-trading-surface text-trading-text-secondary border-trading-border hover:border-trading-success hover:text-trading-success'
              }
            `}
          >
            <TrendingUp className="h-5 w-5 mr-2" />
            BUY
          </button>
          <button
            type="button"
            onClick={() => setOrderType('sell')}
            className={`
              flex items-center justify-center px-4 py-4 rounded-lg font-semibold transition-all duration-200 border-2
              ${orderType === 'sell'
                ? 'bg-trading-danger text-white border-trading-danger shadow-lg scale-105'
                : 'bg-trading-surface text-trading-text-secondary border-trading-border hover:border-trading-danger hover:text-trading-danger'
              }
            `}
          >
            <TrendingDown className="h-5 w-5 mr-2" />
            SELL
          </button>
        </div>

        {/* Current prices */}
        {currentBid !== undefined && currentAsk !== undefined ? (
          <div className="space-y-3">
            <div className="grid grid-cols-2 gap-3">
              {/* Bid Price */}
              <div className="p-3 bg-trading-danger/10 border border-trading-danger/30 rounded-lg">
                <div className="text-xs text-trading-danger mb-1 font-medium">BID (Sell)</div>
                <div className="text-lg font-bold text-trading-text-primary font-mono">
                  {formatPrice(currentBid, precision)}
                </div>
              </div>
              
              {/* Ask Price */}
              <div className="p-3 bg-trading-success/10 border border-trading-success/30 rounded-lg">
                <div className="text-xs text-trading-success mb-1 font-medium">ASK (Buy)</div>
                <div className="text-lg font-bold text-trading-text-primary font-mono">
                  {formatPrice(currentAsk, precision)}
                </div>
              </div>
            </div>
            
            {/* Spread */}
            {spread !== undefined && (
              <div className="p-3 bg-trading-warning/10 border border-trading-warning/30 rounded-lg text-center">
                <div className="text-xs text-trading-warning mb-1 font-medium">Spread</div>
                <div className="text-sm font-bold text-trading-text-primary font-mono">
                  {formatPrice(spread, precision)} ({((spread / currentBid) * 10000).toFixed(1)} pips)
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="p-4 bg-trading-info/10 border border-trading-info/30 rounded-lg">
            <div className="text-xs text-trading-info mb-2 font-medium">Current Price</div>
            <div className="text-xl font-bold text-trading-text-primary font-mono">
              {formatPrice(currentPrice, precision)}
            </div>
          </div>
        )}

        {/* Position size */}
        <div>
          <label className="block text-sm font-medium text-trading-text-primary mb-3">
            Position Size (Lots)
          </label>
          <input
            type="number"
            value={size}
            onChange={(e) => setSize(parseFloat(e.target.value) || 0)}
            min="0.01"
            max="100"
            step="0.01"
            className="trading-input"
            required
          />
          <div className="mt-2 text-xs text-trading-text-secondary">
            Required margin: <span className="text-trading-text-primary font-medium">{formatCurrency(requiredMargin)}</span>
          </div>
        </div>

        {/* Advanced options toggle */}
        <button
          type="button"
          onClick={() => setShowAdvanced(!showAdvanced)}
          className="text-sm text-trading-info hover:text-trading-info/80 font-medium transition-colors"
        >
          {showAdvanced ? 'Hide' : 'Show'} Advanced Options
        </button>

        {/* Advanced options */}
        {showAdvanced && (
          <div className="space-y-4 p-4 bg-trading-surface border border-trading-border rounded-lg animate-fade-in">
            <div>
              <label className="block text-sm font-medium text-trading-text-primary mb-2">
                Stop Loss
              </label>
              <input
                type="number"
                value={stopLoss}
                onChange={(e) => setStopLoss(e.target.value)}
                step="0.00001"
                placeholder={`e.g., ${(() => {
                  const executionPrice = orderType === 'buy' 
                    ? (currentAsk || currentPrice) 
                    : (currentBid || currentPrice);
                  return formatPrice(executionPrice * (orderType === 'buy' ? 0.99 : 1.01), precision);
                })()}`}
                className="trading-input"
              />
              {stopLoss && (
                <div className="mt-2 text-xs text-trading-text-secondary">
                  Potential loss: <span className="text-trading-danger font-medium">{formatCurrency(calculatePotentialPnL(parseFloat(stopLoss)))}</span>
                </div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-trading-text-primary mb-2">
                Take Profit
              </label>
              <input
                type="number"
                value={takeProfit}
                onChange={(e) => setTakeProfit(e.target.value)}
                step="0.00001"
                placeholder={`e.g., ${(() => {
                  const executionPrice = orderType === 'buy' 
                    ? (currentAsk || currentPrice) 
                    : (currentBid || currentPrice);
                  return formatPrice(executionPrice * (orderType === 'buy' ? 1.01 : 0.99), precision);
                })()}`}
                className="trading-input"
              />
              {takeProfit && (
                <div className="mt-2 text-xs text-trading-text-secondary">
                  Potential profit: <span className="text-trading-success font-medium">{formatCurrency(calculatePotentialPnL(parseFloat(takeProfit)))}</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Submit button */}
        <button
          type="submit"
          disabled={!canTrade || !canAfford || size <= 0}
          className={`
            w-full flex items-center justify-center px-6 py-4 rounded-lg font-semibold transition-all duration-200 text-lg
            ${canTrade && canAfford && size > 0
              ? orderType === 'buy'
                ? 'trading-button-success shadow-lg hover:scale-105'
                : 'trading-button-danger shadow-lg hover:scale-105'
              : 'bg-trading-text-muted/20 text-trading-text-muted cursor-not-allowed'
            }
          `}
        >
          <DollarSign className="h-5 w-5 mr-2" />
          {!canTrade
            ? 'Trading Disabled'
            : !canAfford
              ? 'Insufficient Margin'
              : `${orderType.toUpperCase()} ${size} Lots`
          }
        </button>

        {/* Warnings */}
        {!canAfford && canTrade && (
          <div className="p-3 bg-trading-danger/10 border border-trading-danger/30 rounded-lg animate-fade-in">
            <div className="text-sm text-trading-danger">
              Insufficient margin. Required: {formatCurrency(requiredMargin)},
              Available: {formatCurrency(freeMargin)}
            </div>
          </div>
        )}
      </form>

      {/* Quick size buttons */}
      <div className="mt-6 pt-4 border-t border-trading-border">
        <div className="text-xs text-trading-text-secondary mb-3 font-medium">Quick Size</div>
        <div className="grid grid-cols-4 gap-2">
          {[0.01, 0.1, 0.5, 1.0].map((quickSize) => (
            <button
              key={quickSize}
              type="button"
              onClick={() => setSize(quickSize)}
              className="px-3 py-2 text-xs bg-trading-surface border border-trading-border hover:border-trading-info hover:bg-trading-info/10 rounded-lg transition-all duration-200 text-trading-text-primary font-medium"
            >
              {quickSize}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
