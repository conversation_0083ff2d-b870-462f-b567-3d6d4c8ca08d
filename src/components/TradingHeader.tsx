'use client';

import React from 'react';
import {
  TrendingUp,
  Settings,
  Download,
  Upload,
  BarChart3,
  Wifi,
  WifiOff
} from 'lucide-react';

interface TradingHeaderProps {
  symbol: string;
  totalCandles: number;
  currentBalance: number;
  totalPnL: number;
  onImportData: () => void;
  onExportData?: () => void;
  onSettings?: () => void;
  isConnected?: boolean;
  currentBid?: number;
  currentAsk?: number;
  spread?: number;
  precision?: number;
}

export default function TradingHeader({
  symbol,
  totalCandles,
  currentBalance,
  totalPnL,
  onImportData,
  onExportData,
  onSettings,
  isConnected = true,
  currentBid,
  currentAsk,
  spread,
  precision = 5
}: TradingHeaderProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  const formatPrice = (value: number) => {
    return value.toFixed(precision);
  };

  const pnlPercentage = currentBalance > 0 ? ((totalPnL / (currentBalance - totalPnL)) * 100) : 0;

  return (
    <header className="trading-header sticky top-0 z-50 backdrop-blur-md">
      <div className="max-w-7xl mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo and Title */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-br from-trading-info to-trading-accent rounded-lg flex items-center justify-center">
                <TrendingUp className="h-5 w-5 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-trading-text-primary">
                  FX Backtester
                </h1>
                <div className="text-xs text-trading-text-secondary">
                  Professional Trading Simulation
                </div>
              </div>
            </div>

            {/* Connection Status */}
            <div className="flex items-center space-x-2">
              {isConnected ? (
                <div className="flex items-center space-x-1 text-trading-success">
                  <Wifi className="h-4 w-4" />
                  <span className="text-xs font-medium">Connected</span>
                </div>
              ) : (
                <div className="flex items-center space-x-1 text-trading-danger">
                  <WifiOff className="h-4 w-4" />
                  <span className="text-xs font-medium">Disconnected</span>
                </div>
              )}
            </div>
          </div>

          {/* Market Info */}
          {symbol && (
            <div className="hidden md:flex items-center space-x-6">
              <div className="text-center">
                <div className="text-xs text-trading-text-secondary">Symbol</div>
                <div className="text-sm font-bold text-trading-text-primary">{symbol}</div>
              </div>
              
              {/* Bid/Ask Info */}
              {currentBid !== undefined && currentAsk !== undefined ? (
                <>
                  <div className="text-center">
                    <div className="text-xs text-trading-danger">Bid</div>
                    <div className="text-sm font-bold text-trading-text-primary font-mono">
                      {formatPrice(currentBid)}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-xs text-trading-success">Ask</div>
                    <div className="text-sm font-bold text-trading-text-primary font-mono">
                      {formatPrice(currentAsk)}
                    </div>
                  </div>
                  {spread !== undefined && (
                    <div className="text-center">
                      <div className="text-xs text-trading-warning">Spread</div>
                      <div className="text-sm font-bold text-trading-text-primary font-mono">
                        {formatPrice(spread)}
                      </div>
                    </div>
                  )}
                </>
              ) : (
                <div className="text-center">
                  <div className="text-xs text-trading-text-secondary">Candles</div>
                  <div className="text-sm font-bold text-trading-text-primary">
                    {totalCandles.toLocaleString()}
                  </div>
                </div>
              )}
              
              <div className="text-center">
                <div className="text-xs text-trading-text-secondary">Balance</div>
                <div className="text-sm font-bold text-trading-text-primary">
                  {formatCurrency(currentBalance)}
                </div>
              </div>
              <div className="text-center">
                <div className="text-xs text-trading-text-secondary">P&L</div>
                <div className={`text-sm font-bold ${
                  totalPnL >= 0 ? 'stat-positive' : 'stat-negative'
                }`}>
                  {formatCurrency(totalPnL)}
                  <span className="text-xs ml-1">
                    ({pnlPercentage >= 0 ? '+' : ''}{pnlPercentage.toFixed(2)}%)
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex items-center space-x-2">
            <button
              onClick={onImportData}
              className="trading-button-primary flex items-center space-x-2"
              title="Import Data"
            >
              <Upload className="h-4 w-4" />
              <span className="hidden sm:inline">Import</span>
            </button>

            {onExportData && (
              <button
                onClick={onExportData}
                className="trading-button bg-trading-accent text-white hover:bg-opacity-80"
                title="Export Data"
              >
                <Download className="h-4 w-4" />
                <span className="hidden sm:inline ml-2">Export</span>
              </button>
            )}

            <button
              className="trading-button bg-trading-surface text-trading-text-secondary hover:text-trading-text-primary border border-trading-border"
              title="Analytics"
            >
              <BarChart3 className="h-4 w-4" />
            </button>

            {onSettings && (
              <button
                onClick={onSettings}
                className="trading-button bg-trading-surface text-trading-text-secondary hover:text-trading-text-primary border border-trading-border"
                title="Settings"
              >
                <Settings className="h-4 w-4" />
              </button>
            )}
          </div>
        </div>

        {/* Mobile Market Info */}
        {symbol && (
          <div className="md:hidden mt-4">
            {currentBid !== undefined && currentAsk !== undefined ? (
              <>
                {/* Mobile Bid/Ask Grid */}
                <div className="grid grid-cols-3 gap-4 mb-3">
                  <div className="text-center">
                    <div className="text-xs text-trading-danger">Bid</div>
                    <div className="text-sm font-bold text-trading-text-primary font-mono">
                      {formatPrice(currentBid)}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-xs text-trading-success">Ask</div>
                    <div className="text-sm font-bold text-trading-text-primary font-mono">
                      {formatPrice(currentAsk)}
                    </div>
                  </div>
                  {spread !== undefined && (
                    <div className="text-center">
                      <div className="text-xs text-trading-warning">Spread</div>
                      <div className="text-sm font-bold text-trading-text-primary font-mono">
                        {formatPrice(spread)}
                      </div>
                    </div>
                  )}
                </div>
                {/* Mobile Account Info */}
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-xs text-trading-text-secondary">Symbol</div>
                    <div className="text-sm font-bold text-trading-text-primary">{symbol}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-xs text-trading-text-secondary">Balance</div>
                    <div className="text-sm font-bold text-trading-text-primary">
                      {formatCurrency(currentBalance)}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-xs text-trading-text-secondary">P&L</div>
                    <div className={`text-sm font-bold ${
                      totalPnL >= 0 ? 'stat-positive' : 'stat-negative'
                    }`}>
                      {formatCurrency(totalPnL)}
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <div className="grid grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-xs text-trading-text-secondary">Symbol</div>
                  <div className="text-sm font-bold text-trading-text-primary">{symbol}</div>
                </div>
                <div className="text-center">
                  <div className="text-xs text-trading-text-secondary">Candles</div>
                  <div className="text-sm font-bold text-trading-text-primary">
                    {totalCandles.toLocaleString()}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-xs text-trading-text-secondary">Balance</div>
                  <div className="text-sm font-bold text-trading-text-primary">
                    {formatCurrency(currentBalance)}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-xs text-trading-text-secondary">P&L</div>
                  <div className={`text-sm font-bold ${
                    totalPnL >= 0 ? 'stat-positive' : 'stat-negative'
                  }`}>
                    {formatCurrency(totalPnL)}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </header>
  );
}
