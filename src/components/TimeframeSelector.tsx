'use client';

import React from 'react';
import { Clock } from 'lucide-react';
import { Timeframe } from '@/types/trading';
import { TIMEFRAMES } from '@/utils/timeframeAggregator';

interface TimeframeSelectorProps {
  currentTimeframe: Timeframe;
  onTimeframeChange: (timeframe: Timeframe) => void;
  disabled?: boolean;
}

export default function TimeframeSelector({
  currentTimeframe,
  onTimeframeChange,
  disabled = false
}: TimeframeSelectorProps) {
  const timeframes = Object.values(TIMEFRAMES);

  return (
    <div className="flex items-center space-x-2">
      <Clock className="h-4 w-4 text-trading-text-secondary" />
      <span className="text-sm font-medium text-trading-text-primary">Timeframe:</span>
      
      <div className="flex items-center space-x-1">
        {timeframes.map((tf) => (
          <button
            key={tf.value}
            onClick={() => onTimeframeChange(tf.value)}
            disabled={disabled}
            className={`
              px-2 py-1 text-xs font-medium rounded transition-all duration-200
              ${currentTimeframe === tf.value
                ? 'bg-trading-info text-white shadow-sm'
                : 'bg-trading-surface text-trading-text-secondary hover:bg-trading-border hover:text-trading-text-primary'
              }
              ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
            `}
            title={tf.label}
          >
            {tf.value}
          </button>
        ))}
      </div>
    </div>
  );
}
