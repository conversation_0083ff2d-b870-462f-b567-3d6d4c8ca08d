'use client';

import React from 'react';
import { X, CheckCircle, AlertCircle, AlertTriangle, Info, Loader2 } from 'lucide-react';
import { useErrorStore, AppError, LoadingState } from '@/stores/errorStore';

interface NotificationItemProps {
  error: AppError;
  onRemove: (id: string) => void;
}

function NotificationItem({ error, onRemove }: NotificationItemProps) {
  const getIcon = () => {
    switch (error.type) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'info':
        return <Info className="h-5 w-5 text-blue-500" />;
      default:
        return <Info className="h-5 w-5 text-gray-500" />;
    }
  };

  const getBorderColor = () => {
    switch (error.type) {
      case 'success':
        return 'border-l-green-500';
      case 'error':
        return 'border-l-red-500';
      case 'warning':
        return 'border-l-yellow-500';
      case 'info':
        return 'border-l-blue-500';
      default:
        return 'border-l-gray-500';
    }
  };

  return (
    <div className={`bg-white border-l-4 ${getBorderColor()} shadow-lg rounded-lg p-4 mb-3 animate-slide-in-right`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          {getIcon()}
        </div>
        <div className="ml-3 flex-1">
          <h4 className="text-sm font-semibold text-gray-900">
            {error.title}
          </h4>
          <p className="text-sm text-gray-600 mt-1">
            {error.message}
          </p>
          {error.action && (
            <button
              onClick={error.action.handler}
              className="mt-2 text-sm font-medium text-blue-600 hover:text-blue-500"
            >
              {error.action.label}
            </button>
          )}
        </div>
        <div className="ml-4 flex-shrink-0">
          <button
            onClick={() => onRemove(error.id)}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
}

interface LoadingItemProps {
  loading: LoadingState;
}

function LoadingItem({ loading }: LoadingItemProps) {
  return (
    <div className="bg-white border-l-4 border-l-blue-500 shadow-lg rounded-lg p-4 mb-3 animate-slide-in-right">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />
        </div>
        <div className="ml-3 flex-1">
          <p className="text-sm font-medium text-gray-900">
            {loading.message}
          </p>
          {loading.progress !== undefined && (
            <div className="mt-2">
              <div className="bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${Math.max(0, Math.min(100, loading.progress))}%` }}
                />
              </div>
              <p className="text-xs text-gray-500 mt-1">
                {Math.round(loading.progress)}%
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

interface GlobalLoadingOverlayProps {
  isVisible: boolean;
}

function GlobalLoadingOverlay({ isVisible }: GlobalLoadingOverlayProps) {
  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 shadow-xl">
        <div className="flex items-center space-x-3">
          <Loader2 className="h-6 w-6 text-blue-500 animate-spin" />
          <span className="text-lg font-medium text-gray-900">Loading...</span>
        </div>
      </div>
    </div>
  );
}

export default function NotificationSystem() {
  const { errors, loadingStates, isGlobalLoading, removeError } = useErrorStore();

  return (
    <>
      {/* Notification Container */}
      <div className="fixed top-4 right-4 z-40 w-96 max-w-full">
        {/* Loading States */}
        {loadingStates.map((loading) => (
          <LoadingItem key={loading.id} loading={loading} />
        ))}
        
        {/* Error Notifications */}
        {errors.map((error) => (
          <NotificationItem
            key={error.id}
            error={error}
            onRemove={removeError}
          />
        ))}
      </div>

      {/* Global Loading Overlay */}
      <GlobalLoadingOverlay isVisible={isGlobalLoading} />
    </>
  );
}

// Hook for easy notification usage in components
export const useNotifications = () => {
  const errorStore = useErrorStore();

  return {
    showSuccess: errorStore.showSuccess,
    showError: errorStore.showError,
    showWarning: errorStore.showWarning,
    showInfo: errorStore.showInfo,
    startLoading: errorStore.startLoading,
    updateLoading: errorStore.updateLoading,
    stopLoading: errorStore.stopLoading,
    setGlobalLoading: errorStore.setGlobalLoading,
    clearErrors: errorStore.clearErrors,
    clearAllLoading: errorStore.clearAllLoading
  };
};
