import { 
  ISeriesPrimitive, 
  Time,
  ISeriesApi,
  IChartApi,
  SeriesType,
  SeriesAttachedParameter,
  IPrimitivePaneView,
  PrimitivePaneViewZOrder
} from 'lightweight-charts';

export interface FibonacciPoint {
  time: Time;
  price: number;
}

export interface FibonacciRetracementOptions {
  lineColor?: string;
  lineWidth?: number;
  labelColor?: string;
  labelBackground?: string;
  fontSize?: number;
  showLabels?: boolean;
  showPrices?: boolean;
  levels?: number[];
}

const DEFAULT_LEVELS = [0, 0.236, 0.382, 0.5, 0.618, 0.786, 1];

// Type definitions for canvas rendering
interface CanvasRenderingTarget2D {
  useMediaCoordinateSpace(func: (scope: MediaCoordinatesRenderingScope) => void): void;
}

interface MediaCoordinatesRenderingScope {
  context: CanvasRenderingContext2D;
  mediaSize: {
    width: number;
    height: number;
  };
}

export class FibonacciRetracement implements ISeriesPrimitive<Time> {
  private _series: ISeriesApi<SeriesType>;
  private _chart: IChartApi;
  private _startPoint: FibonacciPoint | null = null;
  private _endPoint: FibonacciPoint | null = null;
  private _options: Required<FibonacciRetracementOptions>;
  private _isDrawing: boolean = false;
  private _requestUpdate?: () => void;

  constructor(series: ISeriesApi<SeriesType>, chart: IChartApi, options: FibonacciRetracementOptions = {}) {
    this._series = series;
    this._chart = chart;
    this._options = {
      lineColor: options.lineColor || '#2196F3',
      lineWidth: options.lineWidth || 1,
      labelColor: options.labelColor || '#000000',
      labelBackground: options.labelBackground || 'rgba(255, 255, 255, 0.85)',
      fontSize: options.fontSize || 12,
      showLabels: options.showLabels !== false,
      showPrices: options.showPrices !== false,
      levels: options.levels || DEFAULT_LEVELS,
    };
  }

  attached(param: SeriesAttachedParameter<Time>): void {
    this._requestUpdate = param.requestUpdate;
  }

  detached(): void {
    this._requestUpdate = undefined;
  }

  public startDrawing(point: FibonacciPoint): void {
    this._startPoint = point;
    this._endPoint = null;
    this._isDrawing = true;
    this.requestUpdate();
  }

  public updateEndPoint(point: FibonacciPoint): void {
    if (!this._isDrawing || !this._startPoint) return;
    this._endPoint = point;
    this.requestUpdate();
  }

  public finishDrawing(): void {
    this._isDrawing = false;
    this.requestUpdate();
  }

  public clear(): void {
    this._startPoint = null;
    this._endPoint = null;
    this._isDrawing = false;
    this.requestUpdate();
  }

  public isDrawing(): boolean {
    return this._isDrawing;
  }

  public setOptions(options: Partial<FibonacciRetracementOptions>): void {
    this._options = { ...this._options, ...options };
    this.requestUpdate();
  }

  private requestUpdate(): void {
    if (this._requestUpdate) {
      this._requestUpdate();
    }
  }

  public paneViews(): readonly IPrimitivePaneView[] {
    // Show the fibonacci even after drawing is finished
    if (!this._startPoint) {
      return [];
    }
    
    // If we're drawing and don't have an endpoint yet, don't render
    if (this._isDrawing && !this._endPoint) {
      return [];
    }
    
    // If we have both points or we're actively drawing with an endpoint, render
    if (!this._endPoint) {
      return [];
    }

    return [
      {
        zOrder: (): PrimitivePaneViewZOrder => 'normal',
        renderer: () => ({
          draw: (target: CanvasRenderingTarget2D) => {
            const timeScale = this._chart.timeScale();
            
            // Convert logical points to coordinates
            const startX = timeScale.timeToCoordinate(this._startPoint!.time);
            const endX = timeScale.timeToCoordinate(this._endPoint!.time);
            
            if (startX === null || endX === null) {
              return;
            }

            target.useMediaCoordinateSpace((scope: MediaCoordinatesRenderingScope) => {
              const ctx = scope.context;
              ctx.save();

              try {
                // Use the series' coordinate conversion method
                const startY = this._series.priceToCoordinate(this._startPoint!.price);
                const endY = this._series.priceToCoordinate(this._endPoint!.price);
                
                if (startY === null || endY === null) {
                  return;
                }
                
                // Calculate price difference
                const priceDiff = this._endPoint!.price - this._startPoint!.price;

                // Draw Fibonacci levels
                this._options.levels.forEach((level) => {
                  const levelPrice = this._startPoint!.price + priceDiff * level;
                  const levelY = this._series.priceToCoordinate(levelPrice);
                  
                  if (levelY === null) return;

                  // Draw level line
                  ctx.strokeStyle = this._options.lineColor;
                  ctx.lineWidth = this._options.lineWidth;
                  ctx.setLineDash(level === 0 || level === 1 ? [] : [5, 5]);
                  
                  ctx.beginPath();
                  ctx.moveTo(Math.min(startX, endX), levelY);
                  ctx.lineTo(Math.max(startX, endX), levelY);
                  ctx.stroke();

                  // Draw labels
                  if (this._options.showLabels) {
                    const labelText = `${(level * 100).toFixed(1)}%`;
                    const priceText = levelPrice.toFixed(5);
                    const text = this._options.showPrices ? `${labelText} (${priceText})` : labelText;

                    ctx.font = `${this._options.fontSize}px Arial`;
                    const textWidth = ctx.measureText(text).width;
                    const padding = 4;
                    const labelX = Math.max(startX, endX) + 10;

                    // Draw label background
                    ctx.fillStyle = this._options.labelBackground;
                    ctx.fillRect(
                      labelX - padding,
                      levelY - this._options.fontSize / 2 - padding,
                      textWidth + padding * 2,
                      this._options.fontSize + padding * 2
                    );

                    // Draw label text
                    ctx.fillStyle = this._options.labelColor;
                    ctx.textBaseline = 'middle';
                    ctx.fillText(text, labelX, levelY);
                  }
                });

                // Draw trend line
                ctx.strokeStyle = this._options.lineColor;
                ctx.lineWidth = this._options.lineWidth + 1;
                ctx.setLineDash([]);
                
                ctx.beginPath();
                ctx.moveTo(startX, startY);
                ctx.lineTo(endX, endY);
                ctx.stroke();

                // Draw points
                const drawPoint = (x: number, y: number) => {
                  ctx.fillStyle = this._options.lineColor;
                  ctx.beginPath();
                  ctx.arc(x, y, 4, 0, 2 * Math.PI);
                  ctx.fill();
                };

                drawPoint(startX, startY);
                drawPoint(endX, endY);

              } finally {
                ctx.restore();
              }
            });
          },
        }),
      },
    ];
  }

  public priceAxisViews() {
    return [];
  }

  public timeAxisViews() {
    return [];
  }
}