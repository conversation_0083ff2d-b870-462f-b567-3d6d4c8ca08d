'use client';

import React, { useState } from 'react';
import { X, Settings, DollarSign, TrendingUp, Shield } from 'lucide-react';
import { TradingSettings } from '@/types/trading';
import { formatCurrency } from '@/utils/tradingCalculations';

interface SettingsPanelProps {
  isOpen: boolean;
  onClose: () => void;
  settings: TradingSettings;
  onSettingsChange: (settings: TradingSettings) => void;
}

export default function SettingsPanel({
  isOpen,
  onClose,
  settings,
  onSettingsChange
}: SettingsPanelProps) {
  const [localSettings, setLocalSettings] = useState<TradingSettings>(settings);

  const handleSave = () => {
    onSettingsChange(localSettings);
    onClose();
  };

  const handleReset = () => {
    setLocalSettings(settings);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-trading-background border border-trading-border rounded-lg shadow-xl w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-trading-border">
          <div className="flex items-center">
            <Settings className="h-5 w-5 text-trading-info mr-2" />
            <h2 className="text-lg font-semibold text-trading-text-primary">Trading Settings</h2>
          </div>
          <button
            onClick={onClose}
            className="text-trading-text-secondary hover:text-trading-text-primary transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Commission Settings */}
          <div className="space-y-4">
            <div className="flex items-center">
              <DollarSign className="h-4 w-4 text-trading-warning mr-2" />
              <h3 className="text-sm font-medium text-trading-text-primary">Commission</h3>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-trading-text-primary mb-2">
                Commission per Lot (USD)
              </label>
              <input
                type="number"
                value={localSettings.commissionPerLot}
                onChange={(e) => setLocalSettings(prev => ({
                  ...prev,
                  commissionPerLot: parseFloat(e.target.value) || 0
                }))}
                min="0"
                step="0.01"
                className="trading-input"
                placeholder="7.00"
              />
              <div className="mt-2 text-xs text-trading-text-secondary">
                Commission charged for opening and closing positions
              </div>
            </div>

            {/* Commission Preview */}
            <div className="p-3 bg-trading-surface border border-trading-border rounded-lg">
              <div className="text-xs text-trading-text-secondary mb-2">Commission Examples:</div>
              <div className="space-y-1 text-xs">
                <div className="flex justify-between">
                  <span>0.1 Lot:</span>
                  <span className="text-trading-text-primary font-mono">
                    {formatCurrency(localSettings.commissionPerLot * 0.1)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>1.0 Lot:</span>
                  <span className="text-trading-text-primary font-mono">
                    {formatCurrency(localSettings.commissionPerLot * 1.0)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Round Trip (Open + Close):</span>
                  <span className="text-trading-text-primary font-mono">
                    {formatCurrency(localSettings.commissionPerLot * 2)}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Leverage Settings */}
          <div className="space-y-4">
            <div className="flex items-center">
              <TrendingUp className="h-4 w-4 text-trading-success mr-2" />
              <h3 className="text-sm font-medium text-trading-text-primary">Leverage</h3>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-trading-text-primary mb-2">
                Leverage Ratio
              </label>
              <select
                value={localSettings.leverage}
                onChange={(e) => setLocalSettings(prev => ({
                  ...prev,
                  leverage: parseInt(e.target.value)
                }))}
                className="trading-input"
              >
                <option value={50}>1:50</option>
                <option value={100}>1:100</option>
                <option value={200}>1:200</option>
                <option value={500}>1:500</option>
              </select>
              <div className="mt-2 text-xs text-trading-text-secondary">
                Higher leverage requires less margin but increases risk
              </div>
            </div>
          </div>

          {/* Margin Settings */}
          <div className="space-y-4">
            <div className="flex items-center">
              <Shield className="h-4 w-4 text-trading-danger mr-2" />
              <h3 className="text-sm font-medium text-trading-text-primary">Margin</h3>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-trading-text-primary mb-2">
                Margin Requirement (%)
              </label>
              <input
                type="number"
                value={localSettings.marginRequirement * 100}
                onChange={(e) => setLocalSettings(prev => ({
                  ...prev,
                  marginRequirement: (parseFloat(e.target.value) || 0) / 100
                }))}
                min="0.1"
                max="100"
                step="0.1"
                className="trading-input"
                placeholder="1.0"
              />
              <div className="mt-2 text-xs text-trading-text-secondary">
                Percentage of position value required as margin
              </div>
            </div>

            {/* Margin Preview */}
            <div className="p-3 bg-trading-surface border border-trading-border rounded-lg">
              <div className="text-xs text-trading-text-secondary mb-2">
                Margin Required (1 Lot @ 1.1000):
              </div>
              <div className="text-sm font-mono text-trading-text-primary">
                {formatCurrency(110000 / localSettings.leverage)}
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-between p-6 border-t border-trading-border">
          <button
            onClick={handleReset}
            className="trading-button bg-trading-surface text-trading-text-secondary hover:text-trading-text-primary border border-trading-border"
          >
            Reset
          </button>
          <div className="space-x-3">
            <button
              onClick={onClose}
              className="trading-button bg-trading-surface text-trading-text-secondary hover:text-trading-text-primary border border-trading-border"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              className="trading-button-primary"
            >
              Save Settings
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
