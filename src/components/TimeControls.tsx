'use client';

import React from 'react';
import { <PERSON>, Pause, Ski<PERSON><PERSON>or<PERSON>, <PERSON><PERSON><PERSON><PERSON>, RotateCcw, Clock } from 'lucide-react';

interface TimeControlsProps {
  isPlaying: boolean;
  currentIndex: number;
  totalCandles: number;
  playbackSpeed: number;
  onTogglePlay: () => void;
  onStepForward: () => void;
  onStepBackward: () => void;
  onReset: () => void;
  onSpeedChange: (speed: number) => void;
  currentCandle?: {
    date: string;
    time: string;
    open: number;
    high: number;
    low: number;
    close: number;
  };
}

export default function TimeControls({
  isPlaying,
  currentIndex,
  totalCandles,
  playbackSpeed,
  onTogglePlay,
  onStepForward,
  onStepBackward,
  onReset,
  onSpeedChange,
  currentCandle
}: TimeControlsProps) {
  const progress = totalCandles > 0 ? (currentIndex / (totalCandles - 1)) * 100 : 0;
  const isAtEnd = currentIndex >= totalCandles - 1;
  const isAtStart = currentIndex === 0;

  const speedOptions = [
    { value: 100, label: 'Fast (100ms)' },
    { value: 500, label: 'Medium (500ms)' },
    { value: 1000, label: 'Normal (1s)' },
    { value: 2000, label: 'Slow (2s)' },
  ];

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Clock className="h-5 w-5 text-gray-500" />
          <h3 className="text-sm font-medium text-gray-900">Playback Controls</h3>
        </div>
        
        {currentCandle && (
          <div className="text-xs text-gray-500">
            {currentCandle.date} {currentCandle.time}
          </div>
        )}
      </div>

      {/* Control buttons */}
      <div className="flex items-center justify-center space-x-2 mb-4">
        <button
          onClick={onStepBackward}
          disabled={isAtStart}
          className="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          title="Step Backward"
        >
          <SkipBack className="h-4 w-4" />
        </button>
        
        <button
          onClick={onTogglePlay}
          disabled={isAtEnd}
          className="p-3 rounded-lg bg-blue-500 hover:bg-blue-600 text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          title={isPlaying ? 'Pause' : 'Play'}
        >
          {isPlaying ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5" />}
        </button>
        
        <button
          onClick={onStepForward}
          disabled={isAtEnd}
          className="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          title="Step Forward"
        >
          <SkipForward className="h-4 w-4" />
        </button>
        
        <button
          onClick={onReset}
          className="p-2 rounded-lg bg-red-100 hover:bg-red-200 text-red-600 transition-colors ml-2"
          title="Reset to Start"
        >
          <RotateCcw className="h-4 w-4" />
        </button>
      </div>

      {/* Speed control */}
      <div className="flex items-center justify-between mb-4">
        <label className="text-xs text-gray-500">Playback Speed:</label>
        <select
          value={playbackSpeed}
          onChange={(e) => onSpeedChange(parseInt(e.target.value))}
          className="text-xs border border-gray-300 rounded px-2 py-1 bg-white"
        >
          {speedOptions.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>

      {/* Progress bar */}
      <div className="space-y-2">
        <div className="flex justify-between text-xs text-gray-500">
          <span>Candle {currentIndex + 1} of {totalCandles}</span>
          <span>{progress.toFixed(1)}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progress}%` }}
          />
        </div>
      </div>

      {/* Current candle info */}
      {currentCandle && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="text-xs text-gray-500 mb-2">Current Candle (OHLC)</div>
          <div className="grid grid-cols-4 gap-2 text-xs">
            <div>
              <div className="text-gray-500">Open</div>
              <div className="font-mono">{currentCandle.open.toFixed(5)}</div>
            </div>
            <div>
              <div className="text-gray-500">High</div>
              <div className="font-mono text-green-600">{currentCandle.high.toFixed(5)}</div>
            </div>
            <div>
              <div className="text-gray-500">Low</div>
              <div className="font-mono text-red-600">{currentCandle.low.toFixed(5)}</div>
            </div>
            <div>
              <div className="text-gray-500">Close</div>
              <div className="font-mono">{currentCandle.close.toFixed(5)}</div>
            </div>
          </div>
        </div>
      )}

      {/* Status indicators */}
      <div className="mt-4 flex items-center justify-between text-xs">
        <div className="flex items-center space-x-4">
          <div className={`flex items-center space-x-1 ${isPlaying ? 'text-green-600' : 'text-gray-500'}`}>
            <div className={`w-2 h-2 rounded-full ${isPlaying ? 'bg-green-500' : 'bg-gray-400'}`} />
            <span>{isPlaying ? 'Playing' : 'Paused'}</span>
          </div>
          
          {isAtEnd && (
            <div className="flex items-center space-x-1 text-orange-600">
              <div className="w-2 h-2 rounded-full bg-orange-500" />
              <span>End Reached</span>
            </div>
          )}
        </div>
        
        <div className="text-gray-500">
          Speed: {playbackSpeed}ms
        </div>
      </div>
    </div>
  );
}
