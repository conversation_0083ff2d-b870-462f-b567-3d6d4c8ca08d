'use client';

import React from 'react';
import { Clock, Zap, Timer } from 'lucide-react';

interface SpeedSelectorProps {
  currentSpeed: number | 'realtime';
  onSpeedChange: (speed: number | 'realtime') => void;
  disabled?: boolean;
  dataType?: 'candle' | 'tick';
}

const SPEED_OPTIONS = [
  { value: 50, label: '50ms', icon: Zap, description: 'Very Fast' },
  { value: 100, label: '100ms', icon: Zap, description: 'Fast' },
  { value: 500, label: '500ms', icon: Timer, description: 'Medium' },
  { value: 1000, label: '1s', icon: Clock, description: 'Normal' },
  { value: 2000, label: '2s', icon: Clock, description: 'Slow' },
  { value: 'realtime' as const, label: 'Real-time', icon: Clock, description: 'Authentic timing' }
];

export default function SpeedSelector({
  currentSpeed,
  onSpeedChange,
  disabled = false,
  dataType = 'candle'
}: SpeedSelectorProps) {
  // Filter options based on data type
  const availableOptions = SPEED_OPTIONS.filter(option => {
    // Real-time mode only available for tick data
    if (option.value === 'realtime') {
      return dataType === 'tick';
    }
    return true;
  });

  const currentOption = availableOptions.find(option => option.value === currentSpeed) || availableOptions[3];

  return (
    <div className="flex items-center space-x-3">
      <div className="flex items-center space-x-2">
        <Clock className="h-4 w-4 text-trading-text-secondary" />
        <span className="text-sm font-medium text-trading-text-primary">Speed:</span>
      </div>

      <div className="relative">
        <select
          value={currentSpeed}
          onChange={(e) => {
            const value = e.target.value;
            onSpeedChange(value === 'realtime' ? 'realtime' : parseInt(value));
          }}
          disabled={disabled}
          className={`
            appearance-none bg-trading-surface border border-trading-border rounded-lg px-3 py-2 pr-8
            text-sm font-medium text-trading-text-primary
            hover:bg-trading-accent hover:border-trading-accent
            focus:outline-none focus:ring-2 focus:ring-trading-primary focus:border-transparent
            transition-all duration-200
            ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
          `}
        >
          {availableOptions.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        
        {/* Custom dropdown arrow */}
        <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
          <svg className="h-4 w-4 text-trading-text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </div>

      {/* Speed indicator */}
      <div className="flex items-center space-x-1 text-xs text-trading-text-muted">
        <currentOption.icon className="h-3 w-3" />
        <span>{currentOption.description}</span>
      </div>
    </div>
  );
}
