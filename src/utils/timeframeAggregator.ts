import { CandleData, Timeframe, TimeframeInfo } from '@/types/trading';

/**
 * Timeframe definitions with their second values
 */
export const TIMEFRAMES: Record<Timeframe, TimeframeInfo> = {
  S1: { value: 'S1', label: '1 Second', seconds: 1 },
  S5: { value: 'S5', label: '5 Seconds', seconds: 5 },
  S10: { value: 'S10', label: '10 Seconds', seconds: 10 },
  S15: { value: 'S15', label: '15 Seconds', seconds: 15 },
  S30: { value: 'S30', label: '30 Seconds', seconds: 30 },
  M1: { value: 'M1', label: '1 Minute', seconds: 60 },
  M5: { value: 'M5', label: '5 Minutes', seconds: 300 },
  M15: { value: 'M15', label: '15 Minutes', seconds: 900 },
  M30: { value: 'M30', label: '30 Minutes', seconds: 1800 },
  H1: { value: 'H1', label: '1 Hour', seconds: 3600 },
  H4: { value: 'H4', label: '4 Hours', seconds: 14400 },
  D1: { value: 'D1', label: '1 Day', seconds: 86400 },
  W1: { value: 'W1', label: '1 Week', seconds: 604800 },
  MN1: { value: 'MN1', label: '1 Month', seconds: 2592000 } // Approximate (30 days)
};

/**
 * TimeframeAggregator converts M1 (base) data to higher timeframes
 */
export class TimeframeAggregator {
  private baseData: CandleData[];

  constructor(baseData: CandleData[]) {
    this.baseData = baseData.sort((a, b) => a.timestamp - b.timestamp);
  }

  /**
   * Aggregate data to specified timeframe
   */
  aggregate(targetTimeframe: Timeframe): CandleData[] {
    if (targetTimeframe === 'M1') {
      return this.baseData;
    }

    const targetSeconds = TIMEFRAMES[targetTimeframe].seconds;
    const aggregatedCandles: CandleData[] = [];

    if (this.baseData.length === 0) {
      return aggregatedCandles;
    }

    let currentGroup: CandleData[] = [];
    let groupStartTime = this.getTimeframeStartTime(this.baseData[0].timestamp, targetSeconds);

    for (const candle of this.baseData) {
      const candleGroupTime = this.getTimeframeStartTime(candle.timestamp, targetSeconds);

      // If this candle belongs to a new timeframe period
      if (candleGroupTime > groupStartTime) {
        // Process the current group
        if (currentGroup.length > 0) {
          const aggregatedCandle = this.aggregateGroup(currentGroup, groupStartTime);
          aggregatedCandles.push(aggregatedCandle);
        }

        // Start new group
        currentGroup = [candle];
        groupStartTime = candleGroupTime;
      } else {
        // Add to current group
        currentGroup.push(candle);
      }
    }

    // Process the last group
    if (currentGroup.length > 0) {
      const aggregatedCandle = this.aggregateGroup(currentGroup, groupStartTime);
      aggregatedCandles.push(aggregatedCandle);
    }

    return aggregatedCandles;
  }

  /**
   * Get the start time of a timeframe period for a given timestamp
   */
  private getTimeframeStartTime(timestamp: number, seconds: number): number {
    const date = new Date(timestamp);

    if (seconds < 60) { // Less than a minute (seconds)
      const totalSeconds = date.getSeconds();
      const periodStart = Math.floor(totalSeconds / seconds) * seconds;

      const startDate = new Date(date);
      startDate.setSeconds(periodStart);
      startDate.setMilliseconds(0);

      return startDate.getTime();
    } else if (seconds < 86400) { // Less than a day (minutes/hours)
      const totalSeconds = date.getHours() * 3600 + date.getMinutes() * 60 + date.getSeconds();
      const periodStart = Math.floor(totalSeconds / seconds) * seconds;

      const startDate = new Date(date);
      startDate.setHours(Math.floor(periodStart / 3600));
      startDate.setMinutes(Math.floor((periodStart % 3600) / 60));
      startDate.setSeconds(periodStart % 60);
      startDate.setMilliseconds(0);

      return startDate.getTime();
    } else if (seconds === 86400) { // Daily
      const startDate = new Date(date);
      startDate.setHours(0, 0, 0, 0);
      return startDate.getTime();
    } else if (seconds === 604800) { // Weekly
      const startDate = new Date(date);
      const dayOfWeek = startDate.getDay();
      const daysToSubtract = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Monday start
      startDate.setDate(startDate.getDate() - daysToSubtract);
      startDate.setHours(0, 0, 0, 0);
      return startDate.getTime();
    } else { // Monthly
      const startDate = new Date(date);
      startDate.setDate(1);
      startDate.setHours(0, 0, 0, 0);
      return startDate.getTime();
    }
  }

  /**
   * Aggregate a group of candles into a single candle
   */
  private aggregateGroup(candles: CandleData[], groupStartTime: number): CandleData {
    if (candles.length === 0) {
      throw new Error('Cannot aggregate empty group');
    }

    // Sort by timestamp to ensure correct order
    const sortedCandles = candles.sort((a, b) => a.timestamp - b.timestamp);

    const firstCandle = sortedCandles[0];
    const lastCandle = sortedCandles[sortedCandles.length - 1];

    // OHLC calculation
    const open = firstCandle.open;
    const close = lastCandle.close;
    const high = Math.max(...sortedCandles.map(c => c.high));
    const low = Math.min(...sortedCandles.map(c => c.low));

    // Volume aggregation
    const tickVol = sortedCandles.reduce((sum, c) => sum + c.tickVol, 0);
    const vol = sortedCandles.reduce((sum, c) => sum + c.vol, 0);

    // Use average spread
    const spread = Math.round(sortedCandles.reduce((sum, c) => sum + c.spread, 0) / sortedCandles.length);

    // Format date and time for the group start time
    const groupDate = new Date(groupStartTime);
    const date = groupDate.toISOString().split('T')[0].replace(/-/g, '.');
    const time = groupDate.toTimeString().split(' ')[0];

    return {
      date,
      time,
      open,
      high,
      low,
      close,
      tickVol,
      vol,
      spread,
      timestamp: groupStartTime
    };
  }

  /**
   * Update base data
   */
  updateBaseData(newData: CandleData[]): void {
    this.baseData = newData.sort((a, b) => a.timestamp - b.timestamp);
  }

  /**
   * Get available timeframes
   */
  static getAvailableTimeframes(): TimeframeInfo[] {
    return Object.values(TIMEFRAMES);
  }

  /**
   * Get timeframe info
   */
  static getTimeframeInfo(timeframe: Timeframe): TimeframeInfo {
    return TIMEFRAMES[timeframe];
  }

  /**
   * Validate if aggregation is possible
   */
  static canAggregate(fromTimeframe: Timeframe, toTimeframe: Timeframe): boolean {
    const fromSeconds = TIMEFRAMES[fromTimeframe].seconds;
    const toSeconds = TIMEFRAMES[toTimeframe].seconds;

    // Can only aggregate to higher timeframes
    return toSeconds >= fromSeconds;
  }

  /**
   * Get the next higher timeframe
   */
  static getNextHigherTimeframe(currentTimeframe: Timeframe): Timeframe | null {
    const timeframes = Object.keys(TIMEFRAMES) as Timeframe[];
    const currentIndex = timeframes.indexOf(currentTimeframe);

    if (currentIndex === -1 || currentIndex === timeframes.length - 1) {
      return null;
    }

    return timeframes[currentIndex + 1];
  }

  /**
   * Get the next lower timeframe
   */
  static getNextLowerTimeframe(currentTimeframe: Timeframe): Timeframe | null {
    const timeframes = Object.keys(TIMEFRAMES) as Timeframe[];
    const currentIndex = timeframes.indexOf(currentTimeframe);

    if (currentIndex === -1 || currentIndex === 0) {
      return null;
    }

    return timeframes[currentIndex - 1];
  }

  /**
   * Calculate how many base candles are needed for one aggregated candle
   */
  static getCandleRatio(baseTimeframe: Timeframe, targetTimeframe: Timeframe): number {
    const baseSeconds = TIMEFRAMES[baseTimeframe].seconds;
    const targetSeconds = TIMEFRAMES[targetTimeframe].seconds;

    return Math.floor(targetSeconds / baseSeconds);
  }
}
