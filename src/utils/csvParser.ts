import <PERSON> from 'papaparse';
import { CandleData, TickData, CSVParseResult, DataType } from '@/types/trading';
import { detectPricePrecision } from './tradingCalculations';

// Auto-detect and parse MetaTrader CSV (candle or tick data)
export function parseMetaTraderCSV(file: File): Promise<CSVParseResult> {
  return new Promise((resolve) => {
    Papa.parse(file, {
      header: false,
      skipEmptyLines: true,
      complete: (results) => {
        const rows = results.data as string[][];
        if (rows.length === 0) {
          resolve({
            dataType: 'candle',
            candleData: [],
            errors: ['File is empty']
          });
          return;
        }

        // Auto-detect data type based on header or first data row
        const dataType = detectDataType(rows);

        if (dataType === 'tick') {
          resolve(parseTickData(rows, file.name));
        } else {
          resolve(parseCandleData(rows, file.name));
        }
      },
      error: (error) => {
        resolve({
          dataType: 'candle',
          candleData: [],
          errors: [`Parse error: ${error.message}`]
        });
      }
    });
  });
}

// Parse candle data (OHLC format)
function parseCandleData(rows: string[][], filename: string): CSVParseResult {
  const errors: string[] = [];
  const data: CandleData[] = [];

  // Skip header row if it exists
  const startIndex = isHeaderRow(rows[0]) ? 1 : 0;

  for (let i = startIndex; i < rows.length; i++) {
    const row = rows[i];

    if (row.length < 9) {
      errors.push(`Row ${i + 1}: Expected 9 columns, got ${row.length}`);
      continue;
    }

    try {
      const candle = parseCandleRow(row, i + 1);
      if (candle) {
        data.push(candle);
      }
    } catch (error) {
      errors.push(`Row ${i + 1}: ${error instanceof Error ? error.message : 'Parse error'}`);
    }
  }

  // Sort data by timestamp
  data.sort((a, b) => a.timestamp - b.timestamp);

  // Detect precision from OHLC data
  const allPrices: number[] = [];
  data.forEach(candle => {
    allPrices.push(candle.open, candle.high, candle.low, candle.close);
  });
  const precision = detectPricePrecision(allPrices);

  return {
    dataType: 'candle',
    candleData: data,
    errors,
    symbol: extractSymbolFromFilename(filename),
    precision
  };
}

// Parse tick data format
function parseTickData(rows: string[][], filename: string): CSVParseResult {
  const errors: string[] = [];
  const tickData: TickData[] = [];

  // Skip header row if it exists
  const startIndex = isTickHeaderRow(rows[0]) ? 1 : 0;

  for (let i = startIndex; i < rows.length; i++) {
    const row = rows[i];

    if (row.length < 7) {
      errors.push(`Row ${i + 1}: Expected 7 columns for tick data, got ${row.length}`);
      continue;
    }

    try {
      const tick = parseTickRow(row, i + 1);
      if (tick) {
        tickData.push(tick);
      }
    } catch (error) {
      errors.push(`Row ${i + 1}: ${error instanceof Error ? error.message : 'Parse error'}`);
    }
  }

  // Sort data by timestamp
  tickData.sort((a, b) => a.timestamp - b.timestamp);

  // Detect precision from tick price data
  const allPrices: number[] = [];
  tickData.forEach(tick => {
    if (tick.bid !== undefined) allPrices.push(tick.bid);
    if (tick.ask !== undefined) allPrices.push(tick.ask);
    if (tick.last !== undefined) allPrices.push(tick.last);
  });
  const precision = detectPricePrecision(allPrices);

  return {
    dataType: 'tick',
    tickData,
    errors,
    symbol: extractSymbolFromFilename(filename),
    precision
  };
}

// Detect data type based on header or column count
function detectDataType(rows: string[][]): DataType {
  if (rows.length === 0) return 'candle';

  const firstRow = rows[0];

  // Check for tick data headers
  if (isTickHeaderRow(firstRow)) {
    return 'tick';
  }

  // Check for candle data headers
  if (isHeaderRow(firstRow)) {
    return 'candle';
  }

  // Auto-detect based on column count and content
  if (firstRow.length >= 7 && firstRow.length <= 7) {
    // Likely tick data (DATE, TIME, BID, ASK, LAST, VOLUME, FLAGS)
    return 'tick';
  } else if (firstRow.length >= 9) {
    // Likely candle data (DATE, TIME, OPEN, HIGH, LOW, CLOSE, TICKVOL, VOL, SPREAD)
    return 'candle';
  }

  return 'candle'; // Default to candle
}

function isHeaderRow(row: string[]): boolean {
  if (!row || row.length === 0) return false;

  // Check if first column looks like a header (contains non-date text)
  const firstCol = row[0]?.toLowerCase();
  return firstCol === 'date' || firstCol === '<date>' || firstCol.includes('date');
}

function isTickHeaderRow(row: string[]): boolean {
  if (!row || row.length === 0) return false;

  // Check for tick data specific headers
  const headers = row.map(h => h?.toLowerCase().trim());
  return headers.includes('bid') || headers.includes('ask') || headers.includes('flags') || headers.includes('<bid>');
}

function parseCandleRow(row: string[]): CandleData | null {
  const [dateStr, timeStr, openStr, highStr, lowStr, closeStr, tickVolStr, volStr, spreadStr] = row;

  // Parse date and time
  const date = dateStr.trim();
  const time = timeStr.trim();

  if (!date || !time) {
    throw new Error('Missing date or time');
  }

  // Convert to timestamp
  const timestamp = parseDateTime(date, time);
  if (isNaN(timestamp)) {
    throw new Error(`Invalid date/time: ${date} ${time}`);
  }

  // Parse numeric values
  const open = parseFloat(openStr);
  const high = parseFloat(highStr);
  const low = parseFloat(lowStr);
  const close = parseFloat(closeStr);
  const tickVol = parseInt(tickVolStr) || 0;
  const vol = parseInt(volStr) || 0;
  const spread = parseInt(spreadStr) || 0;

  // Validate numeric values
  if (isNaN(open) || isNaN(high) || isNaN(low) || isNaN(close)) {
    throw new Error('Invalid OHLC values');
  }

  // Validate OHLC logic
  if (high < Math.max(open, close) || low > Math.min(open, close)) {
    throw new Error('Invalid OHLC relationship');
  }

  return {
    date,
    time,
    open,
    high,
    low,
    close,
    tickVol,
    vol,
    spread,
    timestamp
  };
}

function parseTickRow(row: string[], _rowNumber: number): TickData | null {
  const [dateStr, timeStr, bidStr, askStr, lastStr, volumeStr, flagsStr] = row;

  // Parse date and time
  const date = dateStr.trim();
  const time = timeStr.trim();

  if (!date || !time) {
    throw new Error('Missing date or time');
  }

  // Convert to timestamp with milliseconds precision
  const timestamp = parseTickDateTime(date, time);
  if (isNaN(timestamp)) {
    throw new Error(`Invalid date/time: ${date} ${time}`);
  }

  // Parse optional numeric values
  const bid = bidStr && bidStr.trim() ? parseFloat(bidStr) : undefined;
  const ask = askStr && askStr.trim() ? parseFloat(askStr) : undefined;
  const last = lastStr && lastStr.trim() ? parseFloat(lastStr) : undefined;
  const volume = volumeStr && volumeStr.trim() ? parseInt(volumeStr) : undefined;
  const flags = parseInt(flagsStr) || 0;

  // Validate that at least one price is present
  if (bid === undefined && ask === undefined && last === undefined) {
    throw new Error('No valid price data (BID, ASK, or LAST)');
  }

  // Validate numeric values if present
  if (bid !== undefined && isNaN(bid)) {
    throw new Error('Invalid BID value');
  }
  if (ask !== undefined && isNaN(ask)) {
    throw new Error('Invalid ASK value');
  }
  if (last !== undefined && isNaN(last)) {
    throw new Error('Invalid LAST value');
  }

  return {
    date,
    time,
    bid,
    ask,
    last,
    volume,
    flags,
    timestamp
  };
}

function parseDateTime(dateStr: string, timeStr: string): number {
  // Handle different date formats
  // Expected format: YYYY.MM.DD or YYYY-MM-DD or YYYY/MM/DD
  const normalizedDate = dateStr.replace(/[.-]/g, '/');

  // Expected time format: HH:MM:SS or HH:MM
  const fullDateTime = `${normalizedDate} ${timeStr}`;

  const date = new Date(fullDateTime);
  return date.getTime();
}

function parseTickDateTime(dateStr: string, timeStr: string): number {
  // Handle different date formats
  // Expected format: YYYY.MM.DD or YYYY-MM-DD or YYYY/MM/DD
  const normalizedDate = dateStr.replace(/[.-]/g, '/');

  // Expected time format: HH:MM:SS.mmm (with milliseconds)
  // Example: 11:00:00.054
  const fullDateTime = `${normalizedDate} ${timeStr}`;

  const date = new Date(fullDateTime);
  return date.getTime();
}

function extractSymbolFromFilename(filename: string): string | undefined {
  // Try to extract symbol from filename
  // Common patterns: EURUSD_M1.csv, GBPUSD.csv, etc.
  const match = filename.match(/([A-Z]{6})/);
  return match ? match[1] : undefined;
}

// Convert tick data to candles for a specific timeframe
export function convertTicksToCandles(ticks: TickData[], timeframeSeconds: number = 60): CandleData[] {
  if (!ticks || ticks.length === 0) {
    return [];
  }

  const candles: CandleData[] = [];
  const sortedTicks = [...ticks].sort((a, b) => a.timestamp - b.timestamp);

  let currentGroup: TickData[] = [];
  let groupStartTime = getTimeframeStartTime(sortedTicks[0].timestamp, timeframeSeconds);

  for (const tick of sortedTicks) {
    const tickGroupTime = getTimeframeStartTime(tick.timestamp, timeframeSeconds);

    // If this tick belongs to a new timeframe period
    if (tickGroupTime > groupStartTime) {
      // Process the current group
      if (currentGroup.length > 0) {
        const candle = createCandleFromTicks(currentGroup, groupStartTime);
        if (candle) {
          candles.push(candle);
        }
      }

      // Start new group
      currentGroup = [tick];
      groupStartTime = tickGroupTime;
    } else {
      // Add to current group
      currentGroup.push(tick);
    }
  }

  // Process the last group
  if (currentGroup.length > 0) {
    const candle = createCandleFromTicks(currentGroup, groupStartTime);
    if (candle) {
      candles.push(candle);
    }
  }

  return candles;
}

// Helper function to get timeframe start time
function getTimeframeStartTime(timestamp: number, seconds: number): number {
  const date = new Date(timestamp);

  if (seconds < 60) { // Less than a minute (seconds)
    const totalSeconds = date.getSeconds();
    const periodStart = Math.floor(totalSeconds / seconds) * seconds;

    const startDate = new Date(date);
    startDate.setSeconds(periodStart);
    startDate.setMilliseconds(0);

    return startDate.getTime();
  } else if (seconds < 86400) { // Less than a day (minutes/hours)
    const totalSeconds = date.getHours() * 3600 + date.getMinutes() * 60 + date.getSeconds();
    const periodStart = Math.floor(totalSeconds / seconds) * seconds;

    const startDate = new Date(date);
    startDate.setHours(Math.floor(periodStart / 3600));
    startDate.setMinutes(Math.floor((periodStart % 3600) / 60));
    startDate.setSeconds(periodStart % 60);
    startDate.setMilliseconds(0);

    return startDate.getTime();
  } else { // Day or longer
    const startDate = new Date(date);
    startDate.setHours(0, 0, 0, 0);

    const daysSinceEpoch = Math.floor(startDate.getTime() / 86400000);
    const periodStart = Math.floor(daysSinceEpoch / (seconds / 86400)) * (seconds / 86400);

    return periodStart * 86400000;
  }
}

// Helper function to create a candle from a group of ticks
function createCandleFromTicks(ticks: TickData[], groupStartTime: number): CandleData | null {
  if (ticks.length === 0) {
    return null;
  }

  // Sort ticks by timestamp
  const sortedTicks = ticks.sort((a, b) => a.timestamp - b.timestamp);

  // Extract prices from ticks (prefer bid/ask, fallback to last)
  const prices: number[] = [];
  let totalVolume = 0;
  let tickCount = 0;
  let spreadSum = 0;
  let spreadCount = 0;

  for (const tick of sortedTicks) {
    // Use mid price if both bid and ask are available
    if (tick.bid !== undefined && tick.ask !== undefined) {
      const midPrice = (tick.bid + tick.ask) / 2;
      prices.push(midPrice);
      spreadSum += (tick.ask - tick.bid);
      spreadCount++;
    } else if (tick.bid !== undefined) {
      prices.push(tick.bid);
    } else if (tick.ask !== undefined) {
      prices.push(tick.ask);
    } else if (tick.last !== undefined) {
      prices.push(tick.last);
    }

    if (tick.volume !== undefined) {
      totalVolume += tick.volume;
    }
    tickCount++;
  }

  if (prices.length === 0) {
    return null;
  }

  // Calculate OHLC
  const open = prices[0];
  const close = prices[prices.length - 1];
  const high = Math.max(...prices);
  const low = Math.min(...prices);

  // Calculate average spread (convert to points if needed)
  const avgSpread = spreadCount > 0 ? Math.round((spreadSum / spreadCount) * 100000) : 20; // Default 2 pips

  // Format date and time
  const groupDate = new Date(groupStartTime);
  const date = groupDate.toISOString().split('T')[0].replace(/-/g, '.');
  const time = groupDate.toTimeString().split(' ')[0];

  return {
    date,
    time,
    open,
    high,
    low,
    close,
    tickVol: tickCount,
    vol: totalVolume,
    spread: avgSpread,
    timestamp: groupStartTime
  };
}

// Convert CandleData to chart format
export function convertToChartData(candles: CandleData[]): Array<{ time: any; open: number; high: number; low: number; close: number }> {
  return candles.map(candle => ({
    time: Math.floor(candle.timestamp / 1000) as any, // Convert to seconds for lightweight-charts
    open: candle.open,
    high: candle.high,
    low: candle.low,
    close: candle.close
  }));
}

// Validate CSV file
export function validateCSVFile(file: File): string[] {
  const errors: string[] = [];

  if (!file) {
    errors.push('No file selected');
    return errors;
  }

  if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
    errors.push('File must be a CSV file');
  }

  if (file.size > 50 * 1024 * 1024) { // 50MB limit
    errors.push('File size must be less than 50MB');
  }

  if (file.size === 0) {
    errors.push('File is empty');
  }

  return errors;
}
