import type { Metada<PERSON> } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: "swap",
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "FX Backtester - Professional Forex Trading Simulation",
  description: "Advanced forex backtesting platform with MetaTrader CSV import, real-time candlestick charts, and comprehensive performance analytics for trading strategy development.",
  keywords: ["forex", "backtesting", "trading", "MetaTrader", "charts", "financial", "analysis"],
  authors: [{ name: "FX Backtester" }],
  viewport: "width=device-width, initial-scale=1",
  themeColor: "#1a1a2e",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-trading-surface text-trading-text-primary min-h-screen`}
      >
        {children}
      </body>
    </html>
  );
}
