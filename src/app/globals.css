@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #0a0e27;
  --foreground: #ffffff;
}

@media (prefers-color-scheme: light) {
  :root {
    --background: #f8fafc;
    --foreground: #1e293b;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: var(--font-geist-sans), system-ui, sans-serif;
  font-feature-settings: 'rlig' 1, 'calt' 1;
}

/* Custom scrollbar for trading platform */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #1a1a2e;
}

::-webkit-scrollbar-thumb {
  background: #2a2d47;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #3a3d57;
}

/* Trading platform specific styles */
@layer components {
  .trading-panel {
    @apply bg-trading-secondary border border-trading-border rounded-lg shadow-trading backdrop-blur-sm;
  }

  .trading-button {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-trading-secondary;
  }

  .trading-button-primary {
    @apply trading-button bg-trading-info text-white hover:bg-blue-600 focus:ring-blue-500;
  }

  .trading-button-success {
    @apply trading-button bg-trading-success text-white hover:bg-green-600 focus:ring-green-500;
  }

  .trading-button-danger {
    @apply trading-button bg-trading-danger text-white hover:bg-red-600 focus:ring-red-500;
  }

  .trading-input {
    @apply w-full px-3 py-2 bg-trading-surface border border-trading-border rounded-lg text-trading-text-primary placeholder-trading-text-muted focus:outline-none focus:ring-2 focus:ring-trading-info focus:border-transparent transition-colors;
  }

  .trading-card {
    @apply trading-panel p-6;
  }

  .trading-header {
    @apply bg-trading-primary border-b border-trading-border;
  }

  .stat-positive {
    @apply text-trading-success;
  }

  .stat-negative {
    @apply text-trading-danger;
  }

  .stat-neutral {
    @apply text-trading-text-secondary;
  }
}

/* Animation utilities */
@layer utilities {
  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  @keyframes glow {
    from {
      box-shadow: 0 0 5px rgba(59, 130, 246, 0.2);
    }
    to {
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
    }
  }
}
