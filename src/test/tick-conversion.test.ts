// Test for tick data to candle conversion
import { convertTicksToCandles } from '@/utils/csvParser';
import { TickData } from '@/types/trading';

describe('Tick to Candle Conversion', () => {
  const sampleTicks: TickData[] = [
    {
      date: '2025.05.21',
      time: '11:00:00.054',
      bid: 1.13234,
      ask: 1.13235,
      flags: 6,
      timestamp: new Date('2025-05-21T11:00:00.054Z').getTime()
    },
    {
      date: '2025.05.21',
      time: '11:00:30.119',
      bid: 1.13240,
      ask: 1.13241,
      flags: 6,
      timestamp: new Date('2025-05-21T11:00:30.119Z').getTime()
    },
    {
      date: '2025.05.21',
      time: '11:01:00.183',
      bid: 1.13250,
      ask: 1.13251,
      flags: 6,
      timestamp: new Date('2025-05-21T11:01:00.183Z').getTime()
    }
  ];

  test('should convert ticks to M1 candles', () => {
    const candles = convertTicksToCandles(sampleTicks, 60); // 60 seconds = M1
    
    expect(candles).toHaveLength(2); // Should create 2 M1 candles
    
    // First candle (11:00:00 - 11:00:59)
    expect(candles[0].open).toBe(1.132345); // Mid price of first tick
    expect(candles[0].close).toBe(1.132405); // Mid price of last tick in that minute
    expect(candles[0].high).toBeGreaterThanOrEqual(candles[0].open);
    expect(candles[0].low).toBeLessThanOrEqual(candles[0].open);
    
    // Second candle (11:01:00 - 11:01:59)
    expect(candles[1].open).toBe(1.132505); // Mid price of first tick in second minute
    expect(candles[1].close).toBe(1.132505); // Same as open since only one tick
  });

  test('should handle empty tick data', () => {
    const candles = convertTicksToCandles([], 60);
    expect(candles).toHaveLength(0);
  });

  test('should handle ticks with only bid or ask', () => {
    const ticksWithMissingData: TickData[] = [
      {
        date: '2025.05.21',
        time: '11:00:00.054',
        bid: 1.13234,
        flags: 2,
        timestamp: new Date('2025-05-21T11:00:00.054Z').getTime()
      },
      {
        date: '2025.05.21',
        time: '11:00:30.119',
        ask: 1.13241,
        flags: 4,
        timestamp: new Date('2025-05-21T11:00:30.119Z').getTime()
      }
    ];

    const candles = convertTicksToCandles(ticksWithMissingData, 60);
    expect(candles).toHaveLength(1);
    expect(candles[0].open).toBe(1.13234); // First tick bid
    expect(candles[0].close).toBe(1.13241); // Last tick ask
  });
});
