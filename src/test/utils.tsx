// Test utilities for React components and hooks
import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { act } from '@testing-library/react';

// Mock data for testing
export const mockCandleData = [
  {
    timestamp: 1640995200000, // 2022-01-01 00:00:00
    open: 1.1300,
    high: 1.1350,
    low: 1.1280,
    close: 1.1320,
    volume: 1000,
  },
  {
    timestamp: 1640995260000, // 2022-01-01 00:01:00
    open: 1.1320,
    high: 1.1380,
    low: 1.1310,
    close: 1.1360,
    volume: 1200,
  },
  {
    timestamp: 1640995320000, // 2022-01-01 00:02:00
    open: 1.1360,
    high: 1.1390,
    low: 1.1340,
    close: 1.1370,
    volume: 800,
  },
];

export const mockTickData = [
  {
    timestamp: 1640995200000,
    bid: 1.1300,
    ask: 1.1302,
    last: 1.1301,
    volume: 100,
  },
  {
    timestamp: 1640995201000,
    bid: 1.1301,
    ask: 1.1303,
    last: 1.1302,
    volume: 150,
  },
  {
    timestamp: 1640995202000,
    bid: 1.1302,
    ask: 1.1304,
    last: 1.1303,
    volume: 200,
  },
];

export const mockOrder = {
  id: 'test-order-1',
  type: 'buy' as const,
  size: 0.1,
  entryPrice: 1.1300,
  stopLoss: 1.1250,
  takeProfit: 1.1400,
  timestamp: Date.now(),
  status: 'pending' as const,
  commission: 5,
};

export const mockPosition = {
  id: 'test-position-1',
  orderId: 'test-order-1',
  type: 'buy' as const,
  size: 0.1,
  entryPrice: 1.1300,
  entryTime: Date.now(),
  stopLoss: 1.1250,
  takeProfit: 1.1400,
  unrealizedPnL: 50,
  commission: 5,
  swap: 0,
};

export const mockAccount = {
  balance: 10000,
  equity: 10050,
  margin: 100,
  freeMargin: 9950,
  marginLevel: 10050,
  totalPnL: 50,
  totalCommission: 5,
};

export const mockCSVParseResult = {
  candleData: mockCandleData,
  dataType: 'candle' as const,
  errors: [],
  symbol: 'EURUSD',
  precision: 5,
};

// Custom render function that includes providers if needed
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return <>{children}</>;
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options });

export * from '@testing-library/react';
export { customRender as render };

// Helper to wait for async operations
export const waitForAsync = () => act(async () => {
  await new Promise(resolve => setTimeout(resolve, 0));
});

// Helper to create a mock function with specific return value
export const createMockFunction = <T extends (...args: any[]) => any>(
  returnValue?: ReturnType<T>
) => {
  const mockFn = vi.fn() as T;
  if (returnValue !== undefined) {
    mockFn.mockReturnValue(returnValue);
  }
  return mockFn;
};

// Helper to create a mock async function
export const createMockAsyncFunction = <T extends (...args: any[]) => Promise<any>>(
  returnValue?: Awaited<ReturnType<T>>
) => {
  const mockFn = vi.fn() as T;
  if (returnValue !== undefined) {
    mockFn.mockResolvedValue(returnValue);
  }
  return mockFn;
};

// Store test helpers
export const createMockStore = <T>(initialState: T) => {
  let state = initialState;
  const listeners = new Set<() => void>();

  return {
    getState: () => state,
    setState: (newState: Partial<T>) => {
      state = { ...state, ...newState };
      listeners.forEach(listener => listener());
    },
    subscribe: (listener: () => void) => {
      listeners.add(listener);
      return () => listeners.delete(listener);
    },
    destroy: () => {
      listeners.clear();
    },
  };
};
