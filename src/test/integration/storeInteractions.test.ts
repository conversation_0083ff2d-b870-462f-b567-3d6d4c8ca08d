// Integration tests for store interactions
import { renderHook, act } from '@testing-library/react';
import { useTradingSession, useAccount, useChartInteraction } from '@/hooks';
import { mockCandleData, mockCSVParseResult, waitForAsync } from '@/test/utils';

// Mock the actual stores to test real interactions
vi.mock('@/stores/errorStore', () => ({
  useErrorHandler: () => ({
    handleSyncError: (fn: () => any) => fn(),
    startLoading: vi.fn(),
    updateLoading: vi.fn(),
    stopLoading: vi.fn(),
    showSuccess: vi.fn(),
    showError: vi.fn(),
    showInfo: vi.fn(),
  }),
}));

describe('Store Integration Tests', () => {
  beforeEach(() => {
    // Reset all stores before each test
    vi.clearAllMocks();
  });

  describe('Trading Session and Account Integration', () => {
    it('should handle complete order placement workflow', async () => {
      const { result: sessionResult } = renderHook(() => useTradingSession());
      const { result: accountResult } = renderHook(() => useAccount());

      // 1. Load trading data
      act(() => {
        sessionResult.current.handleDataLoaded(mockCSVParseResult);
      });

      await waitForAsync();

      // Verify data is loaded
      expect(sessionResult.current.hasData).toBe(true);
      expect(sessionResult.current.symbol).toBe('EURUSD');

      // 2. Market data is updated automatically via useEffect
      // No manual call needed

      await waitForAsync();

      // 3. Place an order using current market data
      const orderParams = {
        type: 'buy' as const,
        size: 0.1,
        currentBid: sessionResult.current.currentBid,
        currentAsk: sessionResult.current.currentAsk,
        precision: sessionResult.current.precision || 5,
      };

      let placedOrder: any;
      act(() => {
        placedOrder = accountResult.current.placeOrder(orderParams);
      });

      await waitForAsync();

      // Verify order was placed
      expect(placedOrder).toBeDefined();
      expect(accountResult.current.orders).toContainEqual(
        expect.objectContaining({
          type: 'buy',
          size: 0.1,
        })
      );

      // 4. Simulate market movement and check position updates
      act(() => {
        sessionResult.current.stepForward();
      });

      await waitForAsync();

      // Update position PnLs with new market data
      act(() => {
        accountResult.current.updateAllPositionPnLs(
          sessionResult.current.currentBid,
          sessionResult.current.currentAsk
        );
      });

      await waitForAsync();

      // Verify account equity is updated
      expect(accountResult.current.account.equity).toBeGreaterThan(
        accountResult.current.startBalance
      );
    });

    it('should handle pending order triggers during playback', async () => {
      const { result: sessionResult } = renderHook(() => useTradingSession());
      const { result: accountResult } = renderHook(() => useAccount());

      // Load data and set initial state
      act(() => {
        sessionResult.current.handleDataLoaded(mockCSVParseResult);
      });

      await waitForAsync();

      // Place a pending buy limit order below current price
      const pendingOrderParams = {
        type: 'buy-limit' as const,
        size: 0.1,
        price: 1.1250, // Below current market price
        currentBid: 1.1300,
        currentAsk: 1.1302,
        precision: 5,
      };

      act(() => {
        accountResult.current.placeOrder(pendingOrderParams);
      });

      await waitForAsync();

      // Verify pending order exists
      const pendingOrders = accountResult.current.orders.filter(o => o.status === 'pending');
      expect(pendingOrders).toHaveLength(1);

      // Simulate market movement that triggers the order
      act(() => {
        // Move to next candle
        sessionResult.current.stepForward();
        // Check for pending order triggers
        accountResult.current.checkPendingOrders(1.1240, 1.1242); // Price hits limit
      });

      await waitForAsync();

      // Verify order was executed and position created
      expect(accountResult.current.positions).toHaveLength(1);
      expect(accountResult.current.orders.filter(o => o.status === 'pending')).toHaveLength(0);
    });

    it('should handle stop loss triggers during playback', async () => {
      const { result: sessionResult } = renderHook(() => useTradingSession());
      const { result: accountResult } = renderHook(() => useAccount());

      // Setup initial state
      act(() => {
        sessionResult.current.handleDataLoaded(mockCSVParseResult);
      });

      await waitForAsync();

      // Place a market order with stop loss
      const orderParams = {
        type: 'buy' as const,
        size: 0.1,
        stopLoss: 1.1250,
        currentBid: 1.1300,
        currentAsk: 1.1302,
        precision: 5,
      };

      act(() => {
        accountResult.current.placeOrder(orderParams);
      });

      await waitForAsync();

      // Verify position exists
      expect(accountResult.current.positions).toHaveLength(1);

      // Simulate market movement that hits stop loss
      act(() => {
        sessionResult.current.stepForward();
        accountResult.current.checkStopLossTakeProfit(1.1240, 1.1242); // Price hits SL
      });

      await waitForAsync();

      // Verify position was closed
      expect(accountResult.current.positions).toHaveLength(0);
      expect(accountResult.current.account.balance).toBeLessThan(
        accountResult.current.startBalance
      );
    });
  });

  describe('Chart Interaction and Account Integration', () => {
    it('should handle chart-based order placement', async () => {
      const { result: accountResult } = renderHook(() => useAccount());
      
      const mockOnChartOrderPlace = vi.fn((orderData) => {
        // Simulate chart order placement
        accountResult.current.placeOrder({
          type: orderData.orderType,
          size: orderData.size,
          price: orderData.price,
          currentBid: 1.1300,
          currentAsk: 1.1302,
          precision: 5,
        });
      });

      const { result: chartResult } = renderHook(() => useChartInteraction({
        onChartOrderPlace: mockOnChartOrderPlace,
      }));

      // Simulate right-click context menu order placement
      act(() => {
        chartResult.current.handleContextMenuAction('buy-limit', 1.1250, Date.now());
      });

      await waitForAsync();

      // Verify order was placed through chart interaction
      expect(mockOnChartOrderPlace).toHaveBeenCalledWith({
        orderExecutionType: 'market',
        orderType: 'buy-limit',
        price: 1.1250,
        time: expect.any(Number),
        size: 0.1,
      });

      expect(accountResult.current.orders).toHaveLength(1);
    });

    it('should handle drag and drop order modifications', async () => {
      const { result: accountResult } = renderHook(() => useAccount());

      // Place initial order
      act(() => {
        accountResult.current.placeOrder({
          type: 'buy-limit' as const,
          size: 0.1,
          price: 1.1250,
          currentBid: 1.1300,
          currentAsk: 1.1302,
          precision: 5,
        });
      });

      await waitForAsync();

      const orderId = accountResult.current.orders[0].id;

      const mockDragCallbacks = {
        onDragStart: vi.fn(),
        onDragEnd: vi.fn((lineId, lineType, success) => {
          if (success && lineType === 'pending_order') {
            // Simulate successful drag to new price
            accountResult.current.updateOrder(orderId, { entryPrice: 1.1240 });
          }
        }),
      };

      const { result: chartResult } = renderHook(() => useChartInteraction({
        dragCallbacks: mockDragCallbacks,
      }));

      // Simulate drag and drop operation
      const mockParam = {
        point: { x: 100, y: 200 },
        seriesData: new Map([['series1', { value: 1.1240 }]])
      };

      act(() => {
        chartResult.current.handleMouseDown(mockParam);
        chartResult.current.handleMouseMove(mockParam);
        chartResult.current.handleMouseUp(mockParam);
      });

      await waitForAsync();

      // Verify drag callbacks were called
      expect(mockDragCallbacks.onDragStart).toHaveBeenCalled();
      expect(mockDragCallbacks.onDragEnd).toHaveBeenCalledWith('unknown', 'pending_order', true);

      // Verify order price was updated
      const updatedOrder = accountResult.current.orders.find(o => o.id === orderId);
      expect(updatedOrder?.entryPrice).toBe(1.1240);
    });
  });

  describe('Session Playback and Real-time Updates', () => {
    it('should handle real-time updates during playback', async () => {
      const { result: sessionResult } = renderHook(() => useTradingSession());
      const { result: accountResult } = renderHook(() => useAccount());

      // Setup trading session
      act(() => {
        sessionResult.current.handleDataLoaded(mockCSVParseResult);
      });

      await waitForAsync();

      // Place a position
      act(() => {
        accountResult.current.placeOrder({
          type: 'buy' as const,
          size: 0.1,
          currentBid: 1.1300,
          currentAsk: 1.1302,
          precision: 5,
        });
      });

      await waitForAsync();

      // Start playback
      act(() => {
        sessionResult.current.startPlayback();
      });

      await waitForAsync();

      // Simulate playback steps with position updates
      for (let i = 0; i < 3; i++) {
        act(() => {
          sessionResult.current.stepForward();
          // Market data is updated automatically via useEffect

          // Update positions with new market data
          accountResult.current.updateAllPositionPnLs(
            sessionResult.current.currentBid,
            sessionResult.current.currentAsk
          );

          // Check for SL/TP triggers
          accountResult.current.checkStopLossTakeProfit(
            sessionResult.current.currentBid,
            sessionResult.current.currentAsk
          );
        });

        await waitForAsync();
      }

      // Stop playback
      act(() => {
        sessionResult.current.stopPlayback();
      });

      await waitForAsync();

      // Verify session progressed
      expect(sessionResult.current.currentIndex).toBeGreaterThan(0);
      expect(sessionResult.current.isPlaying).toBe(false);

      // Verify account was updated during playback
      expect(accountResult.current.account.equity).toBeDefined();
    });

    it('should maintain data consistency across store updates', async () => {
      const { result: sessionResult } = renderHook(() => useTradingSession());
      const { result: accountResult } = renderHook(() => useAccount());

      // Load data
      act(() => {
        sessionResult.current.handleDataLoaded(mockCSVParseResult);
      });

      await waitForAsync();

      // Perform multiple operations
      act(() => {
        // Place multiple orders
        accountResult.current.placeOrder({
          type: 'buy' as const,
          size: 0.1,
          currentBid: 1.1300,
          currentAsk: 1.1302,
          precision: 5,
        });

        accountResult.current.placeOrder({
          type: 'sell-limit' as const,
          size: 0.05,
          price: 1.1350,
          currentBid: 1.1300,
          currentAsk: 1.1302,
          precision: 5,
        });

        // Move session forward
        sessionResult.current.stepForward();
        // Market data updates automatically via useEffect
      });

      await waitForAsync();

      // Verify data consistency
      expect(accountResult.current.orders).toHaveLength(2);
      expect(accountResult.current.positions).toHaveLength(1); // One market order executed
      
      // Verify account calculations are consistent
      const totalCommission = accountResult.current.orders.reduce(
        (sum, order) => sum + (order.commission || 0), 0
      );
      expect(accountResult.current.account.totalCommission).toBe(totalCommission);

      // Verify session state is consistent
      expect(sessionResult.current.currentIndex).toBeGreaterThan(0);
      expect(sessionResult.current.currentBid).toBeGreaterThan(0);
      expect(sessionResult.current.currentAsk).toBeGreaterThan(sessionResult.current.currentBid);
    });
  });
});
