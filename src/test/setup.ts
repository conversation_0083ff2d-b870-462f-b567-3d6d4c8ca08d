// Test setup file
import '@testing-library/jest-dom';

// Mock lightweight-charts since it requires canvas
vi.mock('lightweight-charts', () => ({
  createChart: vi.fn(() => ({
    addSeries: vi.fn(() => ({
      setData: vi.fn(),
      createPriceLine: vi.fn(),
      removePriceLine: vi.fn(),
      coordinateToPrice: vi.fn(),
      priceToCoordinate: vi.fn(),
    })),
    subscribeClick: vi.fn(),
    subscribeCrosshairMove: vi.fn(),
    unsubscribeClick: vi.fn(),
    unsubscribeCrosshairMove: vi.fn(),
    applyOptions: vi.fn(),
    remove: vi.fn(),
  })),
  CandlestickSeries: 'CandlestickSeries',
  createSeriesMarkers: vi.fn(() => ({
    setMarkers: vi.fn(),
  })),
  LineStyle: {
    Solid: 0,
    Dotted: 1,
    Dashed: 2,
  },
}));

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => '/',
}));
