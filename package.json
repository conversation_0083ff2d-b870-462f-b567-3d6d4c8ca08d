{"name": "fx-backtester", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest", "test:watch": "vitest --watch", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"@types/papaparse": "^5.3.16", "lightweight-charts": "^5.0.7", "lucide-react": "^0.511.0", "next": "15.1.8", "papaparse": "^5.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.8", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}