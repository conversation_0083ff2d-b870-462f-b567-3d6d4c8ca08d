# Fibonacci Retracement Tool Usage

## Overview
The Fibonacci retracement tool has been integrated into the FX Backtester application. It allows you to draw Fibonacci retracement levels on the price chart to identify potential support and resistance levels.

## How to Use

1. **Activate the Tool**: Click the "Fibonacci" button in the chart header to activate the drawing mode.

2. **Draw the Retracement**:
   - First click: Sets the starting point (0% level)
   - Move your mouse to see a preview of the Fibonacci levels
   - Second click: Sets the ending point (100% level)
   - The tool will draw the following levels:
     - 0% (starting point)
     - 23.6%
     - 38.2%
     - 50%
     - 61.8%
     - 78.6%
     - 100% (ending point)

3. **Clear the Drawing**: Click the "Clear" button to remove the current Fibonacci drawing.

## Features

- **Real-time Preview**: As you move your mouse after the first click, you can see a preview of where the Fibonacci levels will be drawn.
- **Price Labels**: Each level shows both the percentage and the exact price value.
- **Persistent Drawing**: Once drawn, the Fibonacci levels remain on the chart until cleared.
- **Visual Styling**: The tool uses blue lines with dashed lines for intermediate levels and solid lines for the 0% and 100% levels.

## Technical Implementation

The Fibonacci retracement tool is implemented as a custom primitive for TradingView's Lightweight Charts library v5.0.7. It uses the series' price-to-coordinate conversion methods to ensure accurate placement of levels on the chart.

### Key Components:
- `FibonacciRetracement.ts`: The main primitive class that handles drawing logic
- `TradingChart.tsx`: Integration with the main chart component
- Custom drawing using canvas API with proper coordinate transformation

## Known Issues

Currently, the tool requires the chart to have price data loaded to function properly. The Y-coordinate calculation relies on the series' price scale, which needs active data to work correctly.