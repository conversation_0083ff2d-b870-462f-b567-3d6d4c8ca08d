# Developer Guide - FX Backtester

## 🏗️ Architecture Overview

The FX Backtester has been completely refactored from a monolithic structure to a modern, modular architecture. This guide provides comprehensive information for developers working on the project.

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── page.tsx           # Main application page (simplified to ~280 lines)
│   ├── layout.tsx         # Root layout
│   └── globals.css        # Global styles
├── components/            # React components
│   ├── TradingChart.tsx   # Chart component (simplified to ~380 lines)
│   ├── OrderPanel.tsx     # Order placement interface
│   ├── PerformancePanel.tsx # Performance metrics display
│   ├── CSVImporter.tsx    # Data import component
│   ├── NotificationSystem.tsx # Error/success notifications
│   └── ...                # Other UI components
├── hooks/                 # Custom React hooks
│   ├── useTradingSession.ts # Trading session management
│   ├── useAccount.ts      # Account and order management
│   ├── useChartInteraction.ts # Chart interactions
│   ├── useDragAndDrop.ts  # Enhanced drag & drop
│   └── __tests__/         # Hook unit tests
├── stores/                # Zustand state stores
│   ├── tradingSessionStore.ts # Session state management
│   ├── accountStore.ts    # Account and trading state
│   ├── chartInteractionStore.ts # Chart UI state
│   ├── errorStore.ts      # Error handling and notifications
│   └── utils.ts           # Store utilities
├── types/                 # TypeScript definitions
│   ├── trading.ts         # Core trading types
│   └── stores.ts          # Store-specific types
├── utils/                 # Utility functions
│   ├── tradingCalculations.ts # Trading math and logic
│   ├── csvParser.ts       # CSV parsing utilities
│   └── timeframeAggregator.ts # Data aggregation
└── test/                  # Test utilities and integration tests
    ├── setup.ts           # Test configuration
    ├── utils.tsx          # Test helpers and mocks
    └── integration/       # Integration tests
```

## 🔄 State Management Architecture

### Zustand Stores

The application uses Zustand for state management, providing better performance and developer experience compared to React Context.

#### 1. Trading Session Store (`tradingSessionStore.ts`)
Manages all session-related state:
- Market data (candles, ticks)
- Playback state (current index, playing status)
- Market prices (bid, ask, spread)
- Timeframe and update mode settings

```typescript
// Usage example
const tradingSession = useTradingSessionStore();
tradingSession.setData(candleData, baseData, tickData);
tradingSession.setIsPlaying(true);
```

#### 2. Account Store (`accountStore.ts`)
Handles trading account state:
- Account balance and equity
- Orders and positions
- Performance calculations

```typescript
// Usage example
const account = useAccountStore();
account.addOrder(newOrder);
account.processOrderExecution(order, executionPrice);
```

#### 3. Chart Interaction Store (`chartInteractionStore.ts`)
Manages chart UI interactions:
- Fibonacci and trend lines
- Drag and drop state
- Context menu state

```typescript
// Usage example
const chartInteraction = useChartInteractionStore();
chartInteraction.addFibonacciLine(startPrice, endPrice, startTime, endTime);
chartInteraction.showContextMenu(x, y, price, time);
```

#### 4. Error Store (`errorStore.ts`)
Centralized error handling and notifications:
- Error messages and types
- Loading states
- Success notifications

```typescript
// Usage example
const errorHandler = useErrorHandler();
errorHandler.showSuccess('Order Placed', 'Buy order executed successfully');
errorHandler.startLoading('data-processing', 'Loading data...');
```

### Store Benefits

1. **Performance**: Selective subscriptions prevent unnecessary re-renders
2. **DevTools**: Built-in Redux DevTools integration for debugging
3. **Type Safety**: Full TypeScript support with enhanced type definitions
4. **Testability**: Easy to mock and test in isolation

## 🎣 Custom Hooks Architecture

Custom hooks encapsulate business logic and provide a clean API for components.

### 1. useTradingSession Hook

**Purpose**: Manages trading session lifecycle and data operations

**Key Features**:
- Data loading and validation
- Playback controls
- Market data updates
- Timeframe switching

**Usage**:
```typescript
const tradingSession = useTradingSession();

// Load data
tradingSession.handleDataLoaded(csvParseResult);

// Control playback
tradingSession.startPlayback();
tradingSession.stepForward();

// Switch timeframes
tradingSession.switchTimeframe('M5');
```

### 2. useAccount Hook

**Purpose**: Handles all trading account operations

**Key Features**:
- Order placement and validation
- Position management
- Risk calculations
- Performance metrics

**Usage**:
```typescript
const account = useAccount();

// Place order
const order = account.placeOrder({
  type: 'buy',
  size: 0.1,
  currentBid: 1.1300,
  currentAsk: 1.1302,
  precision: 5
});

// Close position
account.closePosition(positionId, currentBid, currentAsk);

// Update SL/TP
account.updatePositionLevels(positionId, newSL, newTP, bid, ask);
```

### 3. useChartInteraction Hook

**Purpose**: Manages chart interactions and drawing tools

**Key Features**:
- Context menu handling
- Drag and drop operations
- Drawing tools (Fibonacci, trend lines)
- Chart event management

**Usage**:
```typescript
const chartInteraction = useChartInteraction({
  onChartOrderPlace: handleOrderPlace,
  dragCallbacks: {
    onDragStart: handleDragStart,
    onDragEnd: handleDragEnd
  }
});

// Handle chart events
chartInteraction.handleRightClick(mouseEvent);
chartInteraction.addFibonacciRetracement(start, end, startTime, endTime);
```

## 🧩 Component Architecture

### Design Principles

1. **Single Responsibility**: Each component has one clear purpose
2. **Presentation vs Logic**: Components focus on rendering, hooks handle logic
3. **Props Interface**: Clear, typed interfaces for all component props
4. **Composition**: Components compose together to build complex UIs

### Component Categories

#### 1. Layout Components
- `page.tsx`: Main application layout (~280 lines, 90% reduction)
- `TradingHeader.tsx`: Application header with navigation
- `SettingsPanel.tsx`: Configuration interface

#### 2. Trading Components
- `TradingChart.tsx`: Chart display (~380 lines, 75% reduction)
- `OrderPanel.tsx`: Order placement interface
- `PerformancePanel.tsx`: Performance metrics display

#### 3. Utility Components
- `CSVImporter.tsx`: Data import interface
- `NotificationSystem.tsx`: Error and success notifications
- `TimeframeSelector.tsx`: Timeframe selection
- `UpdateModeSelector.tsx`: Update mode selection

### Component Communication

Components communicate through:
1. **Props**: Data and event handlers passed down
2. **Hooks**: Shared state and logic through custom hooks
3. **Stores**: Direct store access for specific use cases

## 🧪 Testing Strategy

### Testing Framework
- **Vitest**: Fast unit test runner
- **React Testing Library**: Component and hook testing
- **jsdom**: Browser environment simulation

### Test Categories

#### 1. Unit Tests
Located in `src/hooks/__tests__/`

**Coverage**:
- All custom hooks (useTradingSession, useAccount, useChartInteraction)
- Business logic functions
- Error handling scenarios
- Edge cases and boundary conditions

**Example**:
```typescript
describe('useTradingSession', () => {
  it('should handle data loading successfully', () => {
    const { result } = renderHook(() => useTradingSession());
    act(() => {
      result.current.handleDataLoaded(mockCSVParseResult);
    });
    expect(result.current.hasData).toBe(true);
  });
});
```

#### 2. Integration Tests
Located in `src/test/integration/`

**Coverage**:
- Store interactions
- Complete user workflows
- Cross-hook communication
- Real-time updates during playback

**Example**:
```typescript
describe('Store Integration', () => {
  it('should handle complete order placement workflow', async () => {
    // Test complete flow from data loading to order execution
  });
});
```

### Running Tests

```bash
npm test              # Run all tests
npm run test:watch    # Watch mode
npm run test:coverage # With coverage report
```

## 🔧 Development Workflow

### Adding New Features

1. **Define Types**: Add TypeScript definitions in `src/types/`
2. **Create Store**: Add state management in `src/stores/` if needed
3. **Build Hook**: Encapsulate logic in `src/hooks/`
4. **Create Component**: Build UI in `src/components/`
5. **Write Tests**: Add comprehensive tests
6. **Update Documentation**: Document new features

### Code Style Guidelines

1. **TypeScript**: Use strict typing, avoid `any`
2. **Naming**: Use descriptive names for functions and variables
3. **Comments**: Document complex business logic
4. **Imports**: Use absolute imports with `@/` prefix
5. **Error Handling**: Use centralized error management

### Performance Considerations

1. **Memoization**: Use `useMemo` and `useCallback` judiciously
2. **Store Subscriptions**: Subscribe only to needed state slices
3. **Component Splitting**: Keep components focused and small
4. **Bundle Size**: Monitor bundle size with build analysis

## 🚀 Deployment

### Build Process

```bash
npm run build    # Create production build
npm run start    # Start production server
```

### Environment Variables

```env
NODE_ENV=production
NEXT_PUBLIC_APP_VERSION=1.0.0
```

### Performance Metrics

Current build metrics:
- **Main Bundle**: 81 kB
- **First Load JS**: 186 kB
- **Shared Chunks**: 105 kB

## 📚 Additional Resources

- [Performance Analysis](./PERFORMANCE_ANALYSIS.md)
- [Testing Strategy](./TESTING_STRATEGY.md)
- [API Documentation](./API_DOCS.md)
- [Contributing Guidelines](./CONTRIBUTING.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes with tests
4. Submit a pull request
5. Ensure all checks pass

## 📞 Support

For development questions or issues:
- Create an issue in the repository
- Review existing documentation
- Check test examples for usage patterns
