[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[ ] NAME:Implement Drag-and-Drop Order Management for TradingView Chart DESCRIPTION:Create a comprehensive drag-and-drop system for managing orders, positions, stop-loss, and take-profit levels directly on the TradingView chart, similar to MetaTrader/TradingView functionality.
--[x] NAME:Create Drag State Management Infrastructure DESCRIPTION:Implement core drag state management, line metadata storage, and utility functions for line proximity detection. Create interfaces for tracking draggable lines and their associated order/position data.

Acceptance Criteria:
- Create DragState interface with isDragging, draggedLine, startPrice, currentPrice
- Implement LineMetadata interface linking IPriceLine to order/position data
- Create utility functions for calculating line proximity (tolerance zones)
- Add drag state management hooks with proper cleanup
- Ensure React best practices with useCallback and proper dependencies
--[x] NAME:Add Mouse Event Handlers to Chart Component DESCRIPTION:Implement mousedown, mousemove, and mouseup event handlers for the TradingChart component. Add coordinate-to-price conversion logic and line hit-testing with tolerance zones.

Acceptance Criteria:
- Add mouse event listeners to chart container with proper cleanup
- Implement coordinate-to-price conversion using TradingView chart API
- Create line hit-testing logic with configurable tolerance (e.g., 5-10 pixels)
- Handle drag start detection and state initialization
- Prevent default chart interactions during drag operations
- Use throttling for mousemove events to optimize performance
--[x] NAME:Implement Line Detection and Metadata System DESCRIPTION:Create mapping between IPriceLine objects and their metadata (order ID, position ID, line type). Implement efficient line proximity detection algorithm and line type identification.

Acceptance Criteria:
- Create Map or WeakMap for storing line metadata associations
- Implement line type enum (PENDING_ORDER, POSITION_ENTRY, STOP_LOSS, TAKE_PROFIT)
- Add efficient proximity detection algorithm for multiple lines
- Create helper functions for line identification and data retrieval
- Ensure metadata is properly cleaned up when lines are removed
- Add debugging utilities for line metadata inspection
--[x] NAME:Implement Pending Order Line Dragging DESCRIPTION:Enable dragging of pending order lines with real-time price updates. Add visual feedback during drag operations and create callback system for updating order execution prices.

Acceptance Criteria:
- Enable dragging for pending order lines (status: 'pending')
- Update line price in real-time during drag using IPriceLine.applyOptions()
- Add visual feedback (cursor changes, line highlighting)
- Create callback interface for order price updates
- Implement drag completion logic with price validation
- Add smooth visual transitions and immediate feedback
- Ensure dragged price respects market constraints
--[x] NAME:Add Pending Order Validation and Constraints DESCRIPTION:Implement price validation for different order types (buy/sell limits/stops). Add visual indicators for invalid drag positions and error handling for constraint violations.

Acceptance Criteria:
- Implement validation rules for buyLimit, sellLimit, buyStop, sellStop orders
- Add visual indicators for invalid drag positions (red highlighting, error cursor)
- Create constraint checking against current market price
- Implement drag cancellation for invalid moves
- Add user feedback for validation errors
- Ensure proper rollback on invalid drag completion
- Add minimum distance validation from current price
--[ ] NAME:Implement Stop-Loss and Take-Profit Line Dragging DESCRIPTION:Enable direct dragging of existing SL/TP lines with real-time position updates. Add validation to prevent invalid SL/TP levels and implement line removal functionality.

Acceptance Criteria:
- Enable dragging for existing SL/TP lines on open positions
- Update position stopLoss/takeProfit values in real-time
- Add validation to prevent SL above entry for buy positions (and vice versa)
- Implement line removal by dragging to designated zone or double-click
- Create visual feedback for valid/invalid SL/TP levels
- Add confirmation for line removal operations
- Ensure proper position data updates on drag completion
--[ ] NAME:Implement SL/TP Creation via Position Line Dragging DESCRIPTION:Add functionality to create SL/TP levels by dragging position entry lines. Implement drag direction detection and different behavior for buy vs sell positions.

Acceptance Criteria:
- Detect drag direction (up/down) from position entry lines
- For BUY positions: drag UP creates TP, drag DOWN creates SL
- For SELL positions: drag DOWN creates TP, drag UP creates SL
- Create temporary SL/TP lines during drag operations
- Add visual indicators showing SL vs TP creation intent
- Implement proper validation for new SL/TP levels
- Ensure position entry lines remain non-draggable for price changes
--[ ] NAME:Add Drag Direction Detection and Visual Indicators DESCRIPTION:Implement visual feedback showing SL vs TP creation intent based on drag direction. Add smooth visual transitions, animations, and price tooltips during drag operations.

Acceptance Criteria:
- Add real-time drag direction detection with hysteresis
- Create visual indicators for SL/TP creation (different colors, icons)
- Implement price tooltips showing current drag price
- Add smooth animations for line creation/updates
- Create visual feedback for valid/invalid drag zones
- Add cursor changes to indicate drag state and validity
- Implement smooth transitions between different drag states
--[ ] NAME:Create Callback Integration with Parent Component DESCRIPTION:Define and implement callback interfaces for order/position updates. Add callback props to TradingChart component and integrate with existing order management system in page.tsx.

Acceptance Criteria:
- Define callback interfaces for order price updates, SL/TP modifications
- Add callback props to TradingChart component interface
- Integrate callbacks with existing order management functions in page.tsx
- Implement proper error handling and rollback mechanisms
- Add optimistic updates with fallback on errors
- Ensure callbacks maintain referential stability with useCallback
- Add proper TypeScript types for all callback parameters
--[ ] NAME:Implement Business Logic Validation DESCRIPTION:Add comprehensive price validation rules, market considerations, and minimum distance requirements between SL/TP and entry prices. Create proper error messages and user feedback.

Acceptance Criteria:
- Implement minimum distance validation (e.g., 10 pips from entry)
- Add spread considerations for order placement
- Create comprehensive validation for all order types and market conditions
- Implement proper error messages with user-friendly explanations
- Add validation for market hours and trading session constraints
- Create rollback mechanisms for failed validations
- Add logging and debugging for validation failures
--[ ] NAME:Add Visual Polish and Performance Optimization DESCRIPTION:Implement smooth animations, performance optimizations (throttling, debouncing), accessibility features, and comprehensive error handling. Ensure React best practices for preventing unnecessary re-renders.

Acceptance Criteria:
- Add smooth CSS transitions and animations for all drag operations
- Implement throttling for mousemove events (16ms for 60fps)
- Add debouncing for expensive operations (validation, callbacks)
- Implement accessibility features (keyboard navigation, screen reader support)
- Add comprehensive error boundaries and error handling
- Optimize React renders with useMemo, useCallback, and proper dependencies
- Add performance monitoring and optimization for large datasets
- Implement proper cleanup for all event listeners and timers